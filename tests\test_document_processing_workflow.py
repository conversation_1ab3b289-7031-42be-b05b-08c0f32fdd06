"""
Tests for Document Processing Workflow
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflows.document_processing_workflow import DocumentProcessingWorkflow, DocumentProcessingState


class TestDocumentProcessingWorkflow:
    """Test cases for Document Processing Workflow"""
    
    @pytest.fixture
    def mock_agents(self):
        """Mock all agents used in the workflow"""
        with patch('workflows.document_processing_workflow.DocumentParsingAgent') as mock_parsing, \
             patch('workflows.document_processing_workflow.ContentAnalysisAgent') as mock_content, \
             patch('workflows.document_processing_workflow.PromptGenerationAgent') as mock_prompt, \
             patch('workflows.document_processing_workflow.QualityAssuranceAgent') as mock_qa:
            
            # Mock parsing agent
            mock_parsing.return_value.parse_document = AsyncMock(return_value={
                'agent': 'DocumentParsingAgent',
                'document_id': 'test_doc_123',
                'content': 'Test document content',
                'structure': {'headings': [], 'sections': []},
                'metadata': {'title': 'Test Document', 'document_type': 'RFP'},
                'stored_in_db': True,
                'parsing_status': 'success'
            })
            
            # Mock content analysis agent
            mock_content.return_value.analyze_document_content = AsyncMock(return_value={
                'agent': 'ContentAnalysisAgent',
                'document_id': 'test_doc_123',
                'analysis_result': {
                    'executive_summary': 'Test summary',
                    'key_requirements': [],
                    'compliance_elements': []
                },
                'status': 'success'
            })
            
            # Mock prompt generation agent
            mock_prompt.return_value.generate_section_prompts = AsyncMock(return_value={
                'agent': 'PromptGenerationAgent',
                'document_id': 'test_doc_123',
                'section_prompts': {
                    'Section 1': {'prompt': 'Test prompt 1'},
                    'Section 2': {'prompt': 'Test prompt 2'}
                },
                'total_prompts': 2,
                'status': 'success'
            })
            
            # Mock quality assurance agent
            mock_qa.return_value.validate_document_processing = AsyncMock(return_value={
                'agent': 'QualityAssuranceAgent',
                'validation_report': {
                    'overall_quality_score': 0.85,
                    'passed_checks': ['Document parsed', 'Content analyzed'],
                    'failed_checks': [],
                    'issues_found': [],
                    'recommendations': []
                },
                'status': 'success'
            })
            
            yield {
                'parsing': mock_parsing,
                'content': mock_content,
                'prompt': mock_prompt,
                'qa': mock_qa
            }
    
    @pytest.fixture
    def workflow(self, mock_agents):
        """Create workflow instance with mocked agents"""
        return DocumentProcessingWorkflow()
    
    @pytest.fixture
    def sample_file(self):
        """Create a sample file for testing"""
        content = "# Test Document\n\nThis is a test document for workflow testing."
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        yield tmp_file_path
        
        # Cleanup
        os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_successful_workflow_execution(self, workflow, sample_file, mock_agents):
        """Test successful end-to-end workflow execution"""
        # Test
        result = await workflow.execute(
            file_path=sample_file,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['overall_status'] == 'success'
        assert result['document_info']['file_name'] == "test.txt"
        assert result['document_info']['document_type'] == "RFP"
        assert 'parsing_result' in result
        assert 'content_analysis' in result
        assert 'prompt_generation' in result
        assert 'quality_validation' in result
        
        # Verify all agents were called
        mock_agents['parsing'].return_value.parse_document.assert_called_once()
        mock_agents['content'].return_value.analyze_document_content.assert_called_once()
        mock_agents['prompt'].return_value.generate_section_prompts.assert_called_once()
        mock_agents['qa'].return_value.validate_document_processing.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parsing_failure_workflow(self, workflow, sample_file, mock_agents):
        """Test workflow behavior when parsing fails"""
        # Mock parsing failure
        mock_agents['parsing'].return_value.parse_document.return_value = {
            'agent': 'DocumentParsingAgent',
            'parsing_status': 'error',
            'error': 'Failed to parse document'
        }
        
        # Test
        result = await workflow.execute(
            file_path=sample_file,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['overall_status'] == 'error'
        assert 'Failed to parse document' in result['error']
        assert result['failed_at_step'] == 'parse_document'
        
        # Verify only parsing agent was called
        mock_agents['parsing'].return_value.parse_document.assert_called_once()
        mock_agents['content'].return_value.analyze_document_content.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_content_analysis_failure_workflow(self, workflow, sample_file, mock_agents):
        """Test workflow behavior when content analysis fails"""
        # Mock content analysis failure
        mock_agents['content'].return_value.analyze_document_content.return_value = {
            'agent': 'ContentAnalysisAgent',
            'status': 'error',
            'error': 'Content analysis failed'
        }
        
        # Test
        result = await workflow.execute(
            file_path=sample_file,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['overall_status'] == 'error'
        assert 'Content analysis failed' in result['error']
        
        # Verify parsing and content analysis were called, but not prompt generation
        mock_agents['parsing'].return_value.parse_document.assert_called_once()
        mock_agents['content'].return_value.analyze_document_content.assert_called_once()
        mock_agents['prompt'].return_value.generate_section_prompts.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_prompt_generation_failure_workflow(self, workflow, sample_file, mock_agents):
        """Test workflow behavior when prompt generation fails"""
        # Mock prompt generation failure
        mock_agents['prompt'].return_value.generate_section_prompts.return_value = {
            'agent': 'PromptGenerationAgent',
            'status': 'error',
            'error': 'Prompt generation failed'
        }
        
        # Test
        result = await workflow.execute(
            file_path=sample_file,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['overall_status'] == 'error'
        assert 'Prompt generation failed' in result['error']
        
        # Verify QA was not called
        mock_agents['qa'].return_value.validate_document_processing.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_quality_validation_failure_workflow(self, workflow, sample_file, mock_agents):
        """Test workflow behavior when quality validation fails"""
        # Mock quality validation failure
        mock_agents['qa'].return_value.validate_document_processing.return_value = {
            'agent': 'QualityAssuranceAgent',
            'status': 'error',
            'error': 'Quality validation failed'
        }
        
        # Test
        result = await workflow.execute(
            file_path=sample_file,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['overall_status'] == 'error'
        assert 'Quality validation failed' in result['error']
    
    @pytest.mark.asyncio
    async def test_workflow_exception_handling(self, workflow, mock_agents):
        """Test workflow exception handling"""
        # Mock exception in parsing
        mock_agents['parsing'].return_value.parse_document.side_effect = Exception("Unexpected error")
        
        # Test
        result = await workflow.execute(
            file_path="/nonexistent/file.txt",
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['overall_status'] == 'error'
        assert 'Workflow execution failed' in result['error']
        assert result['failed_at_step'] == 'workflow_execution'
    
    def test_workflow_status(self, workflow):
        """Test workflow status retrieval"""
        status = workflow.get_workflow_status()
        
        # Assertions
        assert status['workflow_name'] == 'DocumentProcessingWorkflow'
        assert status['status'] == 'active'
        assert status['version'] == '1.0.0'
        assert len(status['agents']) == 4
        assert len(status['processing_steps']) > 0
        
        # Check agent status
        agent_names = [agent['agent_name'] for agent in status['agents']]
        expected_agents = [
            'DocumentParsingAgent',
            'ContentAnalysisAgent', 
            'PromptGenerationAgent',
            'QualityAssuranceAgent'
        ]
        
        for expected_agent in expected_agents:
            assert expected_agent in agent_names
    
    @pytest.mark.asyncio
    async def test_parse_document_step(self, workflow, mock_agents):
        """Test individual parse document step"""
        # Create test state
        state = {
            'file_path': '/test/file.txt',
            'file_name': 'test.txt',
            'document_type': 'RFP',
            'workflow_id': 'test_workflow_123'
        }
        
        # Test
        result_state = await workflow._parse_document_step(state)
        
        # Assertions
        assert result_state['current_step'] == 'parse_document'
        assert result_state['status'] == 'Document parsing completed'
        assert 'parsing_result' in result_state
        assert result_state['parsing_result']['parsing_status'] == 'success'
    
    @pytest.mark.asyncio
    async def test_analyze_content_step(self, workflow, mock_agents):
        """Test individual analyze content step"""
        # Create test state with parsing result
        state = {
            'parsing_result': {
                'document_id': 'test_doc_123',
                'parsing_status': 'success'
            },
            'workflow_id': 'test_workflow_123'
        }
        
        # Test
        result_state = await workflow._analyze_content_step(state)
        
        # Assertions
        assert result_state['current_step'] == 'analyze_content'
        assert result_state['status'] == 'Content analysis completed'
        assert 'content_analysis' in result_state
        assert result_state['content_analysis']['status'] == 'success'
    
    @pytest.mark.asyncio
    async def test_generate_prompts_step(self, workflow, mock_agents):
        """Test individual generate prompts step"""
        # Create test state with parsing result
        state = {
            'parsing_result': {
                'document_id': 'test_doc_123',
                'parsing_status': 'success'
            },
            'workflow_id': 'test_workflow_123'
        }
        
        # Test
        result_state = await workflow._generate_prompts_step(state)
        
        # Assertions
        assert result_state['current_step'] == 'generate_prompts'
        assert result_state['status'] == 'Prompt generation completed'
        assert 'prompt_generation' in result_state
        assert result_state['prompt_generation']['status'] == 'success'
    
    @pytest.mark.asyncio
    async def test_validate_quality_step(self, workflow, mock_agents):
        """Test individual validate quality step"""
        # Create test state with all previous results
        state = {
            'parsing_result': {
                'document_id': 'test_doc_123',
                'parsing_status': 'success',
                'structure': {},
                'content': 'test content',
                'metadata': {}
            },
            'content_analysis': {
                'status': 'success',
                'analysis_result': {}
            },
            'prompt_generation': {
                'status': 'success',
                'section_prompts': {}
            },
            'workflow_id': 'test_workflow_123'
        }
        
        # Test
        result_state = await workflow._validate_quality_step(state)
        
        # Assertions
        assert result_state['current_step'] == 'validate_quality'
        assert result_state['status'] == 'Quality validation completed'
        assert 'quality_validation' in result_state
        assert result_state['quality_validation']['status'] == 'success'
    
    @pytest.mark.asyncio
    async def test_finalize_results_step(self, workflow):
        """Test individual finalize results step"""
        # Create test state with all results
        state = {
            'workflow_id': 'test_workflow_123',
            'file_name': 'test.txt',
            'document_type': 'RFP',
            'parsing_result': {'parsing_status': 'success'},
            'content_analysis': {'status': 'success'},
            'prompt_generation': {'status': 'success'},
            'quality_validation': {'status': 'success'}
        }
        
        # Test
        result_state = await workflow._finalize_results_step(state)
        
        # Assertions
        assert result_state['current_step'] == 'finalize_results'
        assert result_state['status'] == 'Document processing completed successfully'
        assert 'final_result' in result_state
        assert result_state['final_result']['overall_status'] == 'success'
        assert 'processing_summary' in result_state['final_result']
    
    @pytest.mark.asyncio
    async def test_handle_error_step(self, workflow):
        """Test error handling step"""
        # Create test state with error
        state = {
            'workflow_id': 'test_workflow_123',
            'file_name': 'test.txt',
            'document_type': 'RFP',
            'error': 'Test error occurred',
            'current_step': 'parse_document'
        }
        
        # Test
        result_state = await workflow._handle_error_step(state)
        
        # Assertions
        assert result_state['current_step'] == 'error_handler'
        assert 'Test error occurred' in result_state['status']
        assert 'final_result' in result_state
        assert result_state['final_result']['overall_status'] == 'error'
        assert result_state['final_result']['failed_at_step'] == 'parse_document'
    
    def test_routing_functions(self, workflow):
        """Test workflow routing functions"""
        # Test successful parsing route
        state_success = {'parsing_result': {'parsing_status': 'success'}}
        assert workflow._route_after_parsing(state_success) == 'success'
        
        # Test failed parsing route
        state_error = {'parsing_result': {'parsing_status': 'error'}}
        assert workflow._route_after_parsing(state_error) == 'error'
        
        # Test successful content analysis route
        state_success = {'content_analysis': {'status': 'success'}}
        assert workflow._route_after_analysis(state_success) == 'success'
        
        # Test failed content analysis route
        state_error = {'content_analysis': {'status': 'error'}}
        assert workflow._route_after_analysis(state_error) == 'error'
    
    def test_generate_processing_summary(self, workflow):
        """Test processing summary generation"""
        # Create test state
        state = {
            'parsing_result': {
                'parsing_status': 'success',
                'structure': {
                    'sections': [{'id': 1}, {'id': 2}],
                    'headings': [{'id': 1}, {'id': 2}, {'id': 3}]
                },
                'stored_in_db': True
            },
            'content_analysis': {'status': 'success'},
            'prompt_generation': {'total_prompts': 5},
            'quality_validation': {
                'validation_report': {'overall_quality_score': 0.85}
            }
        }
        
        # Test
        summary = workflow._generate_processing_summary(state)
        
        # Assertions
        assert summary['document_parsed'] is True
        assert summary['sections_found'] == 2
        assert summary['headings_found'] == 3
        assert summary['content_analyzed'] is True
        assert summary['prompts_generated'] == 5
        assert summary['quality_score'] == 0.85
        assert summary['stored_in_database'] is True


if __name__ == "__main__":
    pytest.main([__file__])
