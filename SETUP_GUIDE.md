# RFP/RFO Document Processing System - Setup Guide

This guide provides detailed instructions for setting up and deploying the RFP/RFO Document Processing System.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Python**: Version 3.8 or higher
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: At least 5GB free space
- **Network**: Internet connection for API access

### Required Accounts and Services
- **Azure OpenAI**: Active subscription with API access
- **Docker**: For running Qdrant vector database
- **Git**: For cloning the repository

## 🔧 Installation Steps

### Step 1: Environment Setup

1. **Install Python 3.8+**
   ```bash
   # Check Python version
   python --version
   
   # If Python is not installed, download from python.org
   ```

2. **Install Docker**
   ```bash
   # Windows/Mac: Download Docker Desktop from docker.com
   # Linux (Ubuntu):
   sudo apt update
   sudo apt install docker.io docker-compose
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

3. **Install Git**
   ```bash
   # Windows: Download from git-scm.com
   # Mac: Install via Homebrew
   brew install git
   # Linux:
   sudo apt install git
   ```

### Step 2: Project Setup

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd AI360-RFP_RFO
   ```

2. **Create Virtual Environment**
   ```bash
   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   # Mac/Linux:
   source venv/bin/activate
   ```

3. **Install Dependencies**
   ```bash
   # Upgrade pip
   pip install --upgrade pip
   
   # Install project dependencies
   pip install -r requirements.txt
   ```

### Step 3: Azure OpenAI Configuration

1. **Create Azure OpenAI Resource**
   - Log into Azure Portal (portal.azure.com)
   - Create a new Azure OpenAI resource
   - Note the endpoint URL and API key

2. **Deploy Required Models**
   ```
   Required Deployments:
   - GPT-4 or GPT-3.5-turbo (for text generation)
   - text-embedding-3-large (for embeddings)
   ```

3. **Configure Environment Variables**
   Create `markpdfdown/.env` file:
   ```env
   # Azure OpenAI Configuration
   AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
   AZURE_OPENAI_API_KEY=your_api_key_here
   AZURE_OPENAI_DEPLOYMENT_NAME=your_gpt_deployment_name
   AZURE_OPENAI_API_VERSION=2024-02-01
   AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-large
   
   # Qdrant Configuration (optional - uses defaults if not specified)
   QDRANT_HOST=localhost
   QDRANT_PORT=6333
   QDRANT_COLLECTION_NAME=rfp_documents
   ```

### Step 4: Vector Database Setup

1. **Start Qdrant Database**
   ```bash
   # Start Qdrant using Docker Compose
   docker-compose -f docker-compose-qdrant.yml up -d
   
   # Verify Qdrant is running
   curl http://localhost:6333/health
   ```

2. **Verify Database Connection**
   ```bash
   # Test database connectivity
   python -c "from services.qdrant_service import qdrant_service; print('Qdrant connected successfully')"
   ```

### Step 5: System Verification

1. **Run Health Check**
   ```bash
   python tests/run_tests.py --health-check
   ```

2. **Run Basic Tests**
   ```bash
   # Run unit tests
   python tests/run_tests.py --unit-only
   
   # Run integration tests (optional)
   python tests/run_tests.py --integration-only
   ```

## 🚀 Running the Application

### Start the Document Processing UI

```bash
# Activate virtual environment (if not already active)
source venv/bin/activate  # Mac/Linux
# or
venv\Scripts\activate     # Windows

# Start the enhanced document processing application
streamlit run ui/document_processing_app.py
```

### Start the Original Procurement UI

```bash
# Start the original procurement application
streamlit run ui/app.py
```

### Access the Application

- Open your web browser
- Navigate to `http://localhost:8501`
- The application should load with the document processing interface

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. Azure OpenAI Connection Issues

**Problem**: "Azure OpenAI credentials not found" error

**Solution**:
```bash
# Check environment file exists
ls markpdfdown/.env

# Verify environment variables are set
python -c "import os; from dotenv import load_dotenv; load_dotenv('markpdfdown/.env'); print('Endpoint:', os.getenv('AZURE_OPENAI_ENDPOINT'))"
```

#### 2. Qdrant Database Connection Issues

**Problem**: Cannot connect to Qdrant database

**Solution**:
```bash
# Check if Docker is running
docker ps

# Restart Qdrant container
docker-compose -f docker-compose-qdrant.yml down
docker-compose -f docker-compose-qdrant.yml up -d

# Check Qdrant logs
docker-compose -f docker-compose-qdrant.yml logs qdrant
```

#### 3. Python Package Issues

**Problem**: Import errors or missing packages

**Solution**:
```bash
# Reinstall requirements
pip install --force-reinstall -r requirements.txt

# Check for conflicting packages
pip check

# Update pip and setuptools
pip install --upgrade pip setuptools
```

#### 4. Memory Issues

**Problem**: Out of memory errors during processing

**Solution**:
- Increase system memory allocation
- Process smaller documents
- Reduce batch sizes in configuration

#### 5. Port Conflicts

**Problem**: Port 8501 or 6333 already in use

**Solution**:
```bash
# For Streamlit (port 8501)
streamlit run ui/document_processing_app.py --server.port 8502

# For Qdrant (port 6333), modify docker-compose-qdrant.yml
# Change port mapping from "6333:6333" to "6334:6333"
```

## 📊 Performance Optimization

### System Tuning

1. **Memory Optimization**
   ```python
   # Adjust chunk sizes in document_processor.py
   chunk_size = 500  # Reduce for lower memory usage
   overlap = 100     # Reduce overlap
   ```

2. **Processing Speed**
   ```python
   # Adjust batch sizes for embeddings
   batch_size = 5    # Reduce for slower systems
   ```

3. **Database Performance**
   ```yaml
   # In docker-compose-qdrant.yml, add memory limits
   services:
     qdrant:
       mem_limit: 2g
       memswap_limit: 2g
   ```

## 🔒 Security Considerations

### API Key Security

1. **Environment Variables**
   - Never commit `.env` files to version control
   - Use strong, unique API keys
   - Rotate keys regularly

2. **Network Security**
   ```bash
   # Restrict Qdrant access (production)
   # Modify docker-compose-qdrant.yml to bind to localhost only
   ports:
     - "127.0.0.1:6333:6333"
   ```

3. **File Upload Security**
   - Validate file types and sizes
   - Scan uploaded files for malware
   - Implement user authentication

## 📈 Monitoring and Maintenance

### Log Monitoring

```bash
# View application logs
tail -f logs/application.log

# Monitor Qdrant logs
docker-compose -f docker-compose-qdrant.yml logs -f qdrant
```

### Database Maintenance

```bash
# Backup Qdrant data
docker exec qdrant_container tar -czf /backup.tar.gz /qdrant/storage

# Clean up old documents (implement custom script)
python scripts/cleanup_old_documents.py
```

### Performance Monitoring

```bash
# Monitor system resources
htop  # Linux/Mac
# or use Task Manager on Windows

# Monitor Docker containers
docker stats
```

## 🚢 Deployment Options

### Local Development
- Use the setup described above
- Suitable for testing and development

### Production Deployment
- Use environment-specific configuration
- Implement proper logging and monitoring
- Set up backup and recovery procedures
- Use production-grade database hosting

### Cloud Deployment
- Azure Container Instances
- AWS ECS/Fargate
- Google Cloud Run
- Kubernetes clusters

## 📞 Support and Resources

### Getting Help

1. **Documentation**: Check `DOCUMENT_PROCESSING_README.md`
2. **Health Check**: Run `python tests/run_tests.py --health-check`
3. **Logs**: Check application and system logs
4. **Community**: Submit issues with detailed reproduction steps

### Useful Commands

```bash
# Complete system restart
docker-compose -f docker-compose-qdrant.yml down
docker-compose -f docker-compose-qdrant.yml up -d
source venv/bin/activate
streamlit run ui/document_processing_app.py

# Quick health check
python tests/run_tests.py --health-check

# Full test suite
python tests/run_tests.py --verbose --coverage
```

## 🎯 Next Steps

After successful setup:

1. **Upload Test Document**: Try processing a sample RFP/RFO document
2. **Explore Features**: Test all UI components and features
3. **Customize Prompts**: Edit and optimize generated prompts
4. **Monitor Performance**: Check processing times and quality scores
5. **Scale Up**: Process larger documents and multiple files

For advanced configuration and customization, refer to the main documentation in `DOCUMENT_PROCESSING_README.md`.
