name: Changelog CI

on:

  pull_request:
    types: [ opened ]

  workflow_dispatch:
    inputs:
      release_version:
        description: 'Release tag (e.g. 1.0.0)'
        required: true
        default: '1.0.0'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Run Changelog CI
        uses: saadmk11/changelog-ci@v1.1.2
        with:
          changelog_filename: CHANGELOG.md
          committer_username: 'github-actions[bot]'
          committer_email: 'github-actions[bot]@users.noreply.github.com'
          release_version: ${{ github.event.inputs.release_version }}
