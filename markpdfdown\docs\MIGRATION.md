# Migration Guide: OpenAI to Azure OpenAI

This guide helps you migrate from regular OpenAI to Azure OpenAI for the MarkPDFDown project.

## What Changed

The application has been updated to use Azure OpenAI instead of regular OpenAI. This provides better control, compliance, and integration with Azure services.

## Environment Variables Migration

### Old OpenAI Environment Variables (no longer used)
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1/
OPENAI_DEFAULT_MODEL=gpt-4o
```

### New Azure OpenAI Environment Variables
```bash
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name_here
AZURE_OPENAI_API_VERSION=2024-02-01
```

## Setting Up Azure OpenAI

### 1. Create Azure OpenAI Resource

1. Go to the [Azure Portal](https://portal.azure.com)
2. Create a new "Azure OpenAI" resource
3. Choose your subscription, resource group, and region
4. Complete the resource creation

### 2. Deploy a Model

1. Go to [Azure OpenAI Studio](https://oai.azure.com)
2. Navigate to "Deployments" section
3. Create a new deployment with a model like:
   - `gpt-4o` (recommended for vision tasks)
   - `gpt-4` 
   - `gpt-35-turbo`
4. Note the deployment name you choose (this becomes your `AZURE_OPENAI_DEPLOYMENT_NAME`)

### 3. Get Your Credentials

1. In the Azure Portal, go to your OpenAI resource
2. In the left sidebar, click "Keys and Endpoint"
3. Copy:
   - **Key 1** or **Key 2** (this becomes your `AZURE_OPENAI_API_KEY`)
   - **Endpoint** (this becomes your `AZURE_OPENAI_ENDPOINT`)

### 4. Update Your Environment

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your Azure values:
   ```bash
   AZURE_OPENAI_API_KEY=your_actual_key_here
   AZURE_OPENAI_ENDPOINT=https://your-actual-resource-name.openai.azure.com/
   AZURE_OPENAI_DEPLOYMENT_NAME=your_actual_deployment_name
   AZURE_OPENAI_API_VERSION=2024-02-01
   ```

## Code Changes Summary

### LLMClient.py
- Changed from `openai.OpenAI` to `openai.AzureOpenAI`
- Updated constructor to accept `azure_endpoint`, `api_version`
- Updated error messages to reflect Azure OpenAI

### main.py
- Updated environment variable names
- Added validation for Azure OpenAI specific variables
- Updated function documentation

### Dependencies
- Updated OpenAI package version to support Azure OpenAI

## Benefits of Azure OpenAI

1. **Data Privacy**: Your data stays within your Azure environment
2. **Compliance**: Better compliance with enterprise requirements
3. **Integration**: Seamless integration with other Azure services
4. **Control**: More control over model deployments and scaling
5. **Regional Availability**: Deploy models in specific Azure regions

## Troubleshooting

### Common Issues

1. **"Import openai could not be resolved"**
   - Install dependencies: `uv sync` or `pip install -e .`

2. **"Please set the AZURE_OPENAI_API_KEY environment variable"**
   - Ensure your `.env` file has the correct variable names and values

3. **"Deployment not found"**
   - Check that your `AZURE_OPENAI_DEPLOYMENT_NAME` matches exactly with your deployment in Azure OpenAI Studio

4. **"Invalid endpoint"**
   - Ensure your `AZURE_OPENAI_ENDPOINT` follows the format: `https://your-resource-name.openai.azure.com/`

### Testing Your Setup

You can test your Azure OpenAI setup with a simple image:

```bash
python main.py < tests/test.jpg > test_output.md
```

If configured correctly, this should convert the image to markdown using your Azure OpenAI deployment.
