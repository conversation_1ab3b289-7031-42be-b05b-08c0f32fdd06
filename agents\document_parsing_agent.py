"""
Document Parsing Agent for RFP/RFO document processing
Specialized in extracting and parsing document structure
"""
import logging
from typing import Dict, List, Any, Optional
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from langchain_openai import AzureChatOpenAI
from services.azure_openai_service import azure_openai_service
from services.document_processor import document_processor, DocumentStructure

logger = logging.getLogger(__name__)


class DocumentParsingAgent:
    """Agent specialized in document parsing and structure extraction"""
    
    def __init__(self):
        """Initialize the document parsing agent"""
        self.agent_name = "DocumentParsingAgent"
        self.capabilities = [
            "pdf_extraction",
            "structure_analysis", 
            "heading_detection",
            "section_identification",
            "table_of_contents_generation"
        ]
        
        self.system_message = """You are a specialized Document Parsing Agent for RFP/RFO processing.
Your primary responsibilities are:

1. DOCUMENT STRUCTURE ANALYSIS:
   - Extract and identify document hierarchy (headings, subheadings, sections)
   - Detect different heading styles (markdown, numbered, caps, etc.)
   - Parse document organization and flow

2. CONTENT SEGMENTATION:
   - Break documents into logical sections
   - Identify section boundaries and relationships
   - Preserve document structure and context

3. METADATA EXTRACTION:
   - Extract document type, title, dates, and other metadata
   - Identify key document characteristics
   - Classify document categories (RFP, RFO, SOW, etc.)

4. QUALITY ASSURANCE:
   - Validate parsing accuracy
   - Ensure structural integrity
   - Report parsing issues or ambiguities

Always provide structured, detailed analysis with clear section boundaries and hierarchical relationships."""
        
        logger.info(f"Initialized {self.agent_name}")
    
    async def parse_document(
        self,
        file_path: str,
        file_name: str,
        document_type: str = "RFP"
    ) -> Dict[str, Any]:
        """
        Parse a document and extract its structure
        
        Args:
            file_path: Path to the document file
            file_name: Original filename
            document_type: Type of document
            
        Returns:
            Parsed document structure and content
        """
        try:
            logger.info(f"{self.agent_name}: Starting document parsing for {file_name}")
            
            # Use document processor to extract content and structure
            result = await document_processor.process_uploaded_file(
                file_path=file_path,
                file_name=file_name,
                document_type=document_type
            )
            
            # Enhance structure analysis with AI
            enhanced_structure = await self._enhance_structure_analysis(
                content=result['content'],
                initial_structure=result['structure']
            )
            
            # Validate parsing quality
            quality_report = await self._validate_parsing_quality(
                content=result['content'],
                structure=enhanced_structure
            )
            
            return {
                'agent': self.agent_name,
                'document_id': result['document_id'],
                'content': result['content'],
                'structure': enhanced_structure,
                'metadata': result['metadata'],
                'quality_report': quality_report,
                'stored_in_db': result['stored_in_db'],
                'parsing_status': 'success'
            }
            
        except Exception as e:
            logger.error(f"{self.agent_name}: Error parsing document {file_name}: {str(e)}")
            return {
                'agent': self.agent_name,
                'parsing_status': 'error',
                'error': str(e),
                'file_name': file_name
            }
    
    async def _enhance_structure_analysis(
        self,
        content: str,
        initial_structure: DocumentStructure
    ) -> Dict[str, Any]:
        """
        Enhance structure analysis using AI
        
        Args:
            content: Document content
            initial_structure: Initial structure from regex parsing
            
        Returns:
            Enhanced structure analysis
        """
        try:
            # Prepare content sample for AI analysis
            content_sample = content[:8000]  # Limit for API
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Analyze this document structure and enhance the parsing:

INITIAL STRUCTURE:
- Headings found: {len(initial_structure.headings)}
- Sections found: {len(initial_structure.sections)}
- Document type: {initial_structure.document_type}

DOCUMENT CONTENT (first 8000 chars):
{content_sample}

Please provide:
1. Enhanced heading detection and classification
2. Improved section boundaries
3. Document type classification
4. Key structural patterns identified
5. Recommendations for better parsing

Format your response as structured analysis.""")
            ]
            
            # Get AI analysis
            ai_response = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.3
            )
            
            # Combine initial structure with AI enhancements
            enhanced_structure = {
                'headings': initial_structure.headings,
                'sections': initial_structure.sections,
                'table_of_contents': initial_structure.table_of_contents,
                'document_type': initial_structure.document_type,
                'metadata': initial_structure.metadata,
                'ai_analysis': ai_response,
                'enhancement_applied': True
            }
            
            return enhanced_structure
            
        except Exception as e:
            logger.error(f"Error enhancing structure analysis: {str(e)}")
            # Return original structure on error
            return {
                'headings': initial_structure.headings,
                'sections': initial_structure.sections,
                'table_of_contents': initial_structure.table_of_contents,
                'document_type': initial_structure.document_type,
                'metadata': initial_structure.metadata,
                'enhancement_applied': False,
                'enhancement_error': str(e)
            }
    
    async def _validate_parsing_quality(
        self,
        content: str,
        structure: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate the quality of document parsing
        
        Args:
            content: Original document content
            structure: Parsed structure
            
        Returns:
            Quality validation report
        """
        try:
            quality_report = {
                'overall_score': 0.0,
                'issues': [],
                'recommendations': [],
                'metrics': {}
            }
            
            # Calculate basic metrics
            content_length = len(content)
            headings_count = len(structure.get('headings', []))
            sections_count = len(structure.get('sections', []))
            
            quality_report['metrics'] = {
                'content_length': content_length,
                'headings_count': headings_count,
                'sections_count': sections_count,
                'avg_section_length': content_length / max(sections_count, 1)
            }
            
            # Quality checks
            score = 1.0
            
            # Check if headings were found
            if headings_count == 0:
                quality_report['issues'].append("No headings detected - document may lack structure")
                score -= 0.3
            elif headings_count < 3:
                quality_report['issues'].append("Very few headings detected - structure may be incomplete")
                score -= 0.1
            
            # Check section distribution
            if sections_count == 0:
                quality_report['issues'].append("No sections identified")
                score -= 0.3
            
            # Check for very long sections (may indicate parsing issues)
            avg_section_length = quality_report['metrics']['avg_section_length']
            if avg_section_length > 5000:
                quality_report['issues'].append("Sections are very long - may need better segmentation")
                score -= 0.1
            
            # Check document type detection
            if structure.get('document_type') == 'unknown':
                quality_report['issues'].append("Document type could not be determined")
                score -= 0.1
            
            # Generate recommendations
            if headings_count < 5:
                quality_report['recommendations'].append("Consider manual review of heading detection")
            
            if avg_section_length > 3000:
                quality_report['recommendations'].append("Consider breaking large sections into subsections")
            
            quality_report['overall_score'] = max(0.0, score)
            
            return quality_report
            
        except Exception as e:
            logger.error(f"Error validating parsing quality: {str(e)}")
            return {
                'overall_score': 0.0,
                'issues': [f"Quality validation failed: {str(e)}"],
                'recommendations': [],
                'metrics': {}
            }
    
    async def extract_key_sections(
        self,
        document_id: str,
        section_types: List[str] = None
    ) -> Dict[str, Any]:
        """
        Extract specific types of sections from a document
        
        Args:
            document_id: Document identifier
            section_types: Types of sections to extract (e.g., 'scope', 'requirements')
            
        Returns:
            Extracted sections
        """
        try:
            if section_types is None:
                section_types = [
                    'scope of work', 'requirements', 'timeline', 'budget',
                    'evaluation criteria', 'submission instructions'
                ]
            
            # Get document structure from database
            from services.qdrant_service import qdrant_service
            doc_structure = qdrant_service.get_document_structure(document_id)
            
            if not doc_structure:
                return {'error': 'Document not found'}
            
            # Search for relevant sections
            extracted_sections = {}
            
            for section_type in section_types:
                # Search for sections matching the type
                matching_sections = []
                
                for chunk in doc_structure.get('chunks', []):
                    content = chunk.get('content', '').lower()
                    if section_type.lower() in content:
                        matching_sections.append(chunk)
                
                if matching_sections:
                    extracted_sections[section_type] = matching_sections
            
            return {
                'agent': self.agent_name,
                'document_id': document_id,
                'extracted_sections': extracted_sections,
                'section_types_found': list(extracted_sections.keys())
            }
            
        except Exception as e:
            logger.error(f"Error extracting key sections: {str(e)}")
            return {
                'agent': self.agent_name,
                'error': str(e)
            }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and capabilities"""
        return {
            'agent_name': self.agent_name,
            'status': 'active',
            'capabilities': self.capabilities,
            'version': '1.0.0'
        }
