# MarkPDFDown Troubleshooting Guide

## 🚨 **Issue: Processing Gets Stuck at Image Conversion**

### **Problem Symptoms:**
- Process hangs at "Converting image output/20250627164953/page_0033.jpg to Markdown"
- No error messages, just stops responding
- CPU usage drops to zero

### **Most Common Causes & Solutions:**

#### 1. **Missing Azure OpenAI Configuration** ⚙️
**Check if environment variables are set:**
```bash
echo $AZURE_OPENAI_API_KEY
echo $AZURE_OPENAI_ENDPOINT
echo $AZURE_OPENAI_DEPLOYMENT_NAME
```

**Solution:**
```bash
# Create .env file in markpdfdown directory
cp .env.example .env
# Edit .env file with your Azure OpenAI credentials
```

#### 2. **Wrong Azure OpenAI Deployment Name** 🎯
**Check your Azure Portal:**
- Go to Azure OpenAI Studio
- Check your deployment name (it might not be "gpt-4o")
- Update your .env file with the correct deployment name

#### 3. **Network/Firewall Issues** 🌐
**Test connection manually:**
```bash
curl -X POST "https://YOUR_ENDPOINT.openai.azure.com/openai/deployments/YOUR_DEPLOYMENT/chat/completions?api-version=2024-02-01" \
  -H "Content-Type: application/json" \
  -H "api-key: YOUR_API_KEY" \
  -d '{"messages":[{"role":"user","content":"Hello"}],"max_tokens":10}'
```

#### 4. **API Rate Limiting** ⏱️
**Check Azure Portal:**
- Go to Azure OpenAI resource
- Check "Quotas" section
- Look for rate limiting errors

#### 5. **Large Image Files** 📸
**Check image size:**
```bash
ls -la output/20250627164953/page_0033.jpg
```
- Images larger than 20MB may cause timeouts
- Azure OpenAI has limits on image size

### **Debug Steps:**

#### Step 1: Run with Enhanced Logging
```bash
python main.py --resume output/20250627164953/ --save-and-cleanup
```
The updated code now includes:
- ✅ Connection test before processing
- ✅ Progress indicators ([33/246])
- ✅ Better error messages
- ✅ Timeout controls (2 minutes per API call)

#### Step 2: Test Connection Only
```bash
# The new code will test connection at startup
# Look for these messages:
# ✅ Azure OpenAI connection test successful!
# OR
# ❌ Azure OpenAI connection test failed: [error details]
```

#### Step 3: Check Specific Image
If it's stuck on a specific image, check:
```bash
# Check if the image is corrupt or too large
file output/20250627164953/page_0033.jpg
ls -la output/20250627164953/page_0033.jpg
```

#### Step 4: Skip Problematic Image
If one image is problematic, you can manually create its markdown file:
```bash
echo "# Page 33\n\n*Could not process this page*" > output/20250627164953/page_0033.jpg.md
```
Then resume processing.

### **Immediate Action Plan:**

1. **Stop the stuck process** (Ctrl+C)
2. **Update your code** with the fixes I just applied
3. **Check your .env configuration**
4. **Run again** - you'll now see connection test and better progress tracking

### **New Features in Updated Code:**

1. **Connection Test**: Tests Azure OpenAI before processing starts
2. **Progress Tracking**: Shows [33/246] progress indicators
3. **Better Error Handling**: Continues processing even if one image fails
4. **Timeout Protection**: 2-minute timeout per API call
5. **Enhanced Logging**: More detailed status messages

### **Expected New Output:**
```
🔧 Checking Azure OpenAI configuration...
✅ AZURE_OPENAI_API_KEY: **********...
✅ AZURE_OPENAI_ENDPOINT: **********...
✅ Azure OpenAI configuration looks good!
🔍 Testing Azure OpenAI connection...
✅ Azure OpenAI connection test successful!
📄 Processing [33/246] page_0033.jpg
🔄 Converting image output/20250627164953/page_0033.jpg to Markdown
🖼️  Starting conversion of image: page_0033.jpg
Attempt 1/3 for API call
Calling Azure OpenAI model: gpt-4o
Processing 1 image(s)
✅ API call successful on attempt 1
✅ Successfully converted image: page_0033.jpg
✅ [33/246] Successfully processed page_0033.jpg
```

### **If Still Stuck:**
- Check Azure Portal for any service outages
- Verify your subscription quota
- Try with a smaller test image first
- Contact Azure support if API is consistently failing
