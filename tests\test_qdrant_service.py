"""
Tests for Qdrant Service
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.qdrant_service import QdrantService


class TestQdrantService:
    """Test cases for Qdrant Service"""
    
    @pytest.fixture
    def mock_qdrant_client(self):
        """Mock Qdrant client"""
        with patch('services.qdrant_service.QdrantClient') as mock_client:
            yield mock_client
    
    @pytest.fixture
    def mock_azure_service(self):
        """Mock Azure OpenAI service"""
        with patch('services.qdrant_service.azure_openai_service') as mock_service:
            mock_service.get_embeddings = AsyncMock(return_value=[[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]])
            mock_service.get_single_embedding = AsyncMock(return_value=[0.1, 0.2, 0.3])
            yield mock_service
    
    @pytest.fixture
    def qdrant_service(self, mock_qdrant_client, mock_azure_service):
        """Create Qdrant service instance with mocked dependencies"""
        with patch.dict(os.environ, {
            'QDRANT_HOST': 'localhost',
            'QDRANT_PORT': '6333',
            'QDRANT_COLLECTION_NAME': 'test_collection'
        }):
            # Mock collection existence check
            mock_collections = Mock()
            mock_collections.collections = [Mock(name='test_collection')]
            mock_qdrant_client.return_value.get_collections.return_value = mock_collections
            
            return QdrantService()
    
    @pytest.mark.asyncio
    async def test_store_document_success(self, qdrant_service, mock_qdrant_client, mock_azure_service):
        """Test successful document storage"""
        # Test data
        document_id = "test_doc_1"
        content = "This is test content for the document."
        metadata = {
            "title": "Test Document",
            "document_type": "RFP",
            "upload_date": datetime.now().isoformat()
        }
        
        # Mock successful upsert
        mock_qdrant_client.return_value.upsert.return_value = True
        
        # Test
        result = await qdrant_service.store_document(document_id, content, metadata)
        
        # Assertions
        assert result is True
        mock_qdrant_client.return_value.upsert.assert_called_once()
        mock_azure_service.get_embeddings.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_store_document_with_chunks(self, qdrant_service, mock_qdrant_client, mock_azure_service):
        """Test document storage with pre-defined chunks"""
        # Test data
        document_id = "test_doc_2"
        content = "Test content"
        metadata = {"title": "Test Document"}
        chunks = [
            {"content": "Chunk 1 content", "metadata": {"chunk_type": "header"}},
            {"content": "Chunk 2 content", "metadata": {"chunk_type": "body"}}
        ]
        
        # Test
        result = await qdrant_service.store_document(document_id, content, metadata, chunks)
        
        # Assertions
        assert result is True
        # Should use provided chunks instead of creating new ones
        mock_azure_service.get_embeddings.assert_called_once_with(["Chunk 1 content", "Chunk 2 content"])
    
    def test_chunk_document(self, qdrant_service):
        """Test document chunking functionality"""
        # Test content
        content = "This is a test document. " * 100  # Create long content
        
        # Test
        chunks = qdrant_service._chunk_document(content, chunk_size=100, overlap=20)
        
        # Assertions
        assert len(chunks) > 1  # Should create multiple chunks
        assert all(isinstance(chunk, dict) for chunk in chunks)
        assert all("content" in chunk and "metadata" in chunk for chunk in chunks)
        
        # Check overlap
        if len(chunks) > 1:
            # Last part of first chunk should overlap with beginning of second chunk
            first_chunk_end = chunks[0]["content"][-20:]
            second_chunk_start = chunks[1]["content"][:20]
            # There should be some overlap in content
            assert len(first_chunk_end) > 0 and len(second_chunk_start) > 0
    
    @pytest.mark.asyncio
    async def test_search_similar_content(self, qdrant_service, mock_qdrant_client, mock_azure_service):
        """Test similar content search"""
        # Mock search results
        mock_result = Mock()
        mock_result.id = "test_id_1"
        mock_result.score = 0.95
        mock_result.payload = {
            "content": "Test content",
            "document_id": "doc_1",
            "chunk_id": 0,
            "title": "Test Document",
            "document_type": "RFP"
        }
        
        mock_qdrant_client.return_value.search.return_value = [mock_result]
        
        # Test
        query = "test query"
        results = await qdrant_service.search_similar_content(query, limit=5)
        
        # Assertions
        assert len(results) == 1
        assert results[0]["id"] == "test_id_1"
        assert results[0]["score"] == 0.95
        assert results[0]["content"] == "Test content"
        assert results[0]["metadata"]["document_id"] == "doc_1"
        
        # Verify API calls
        mock_azure_service.get_single_embedding.assert_called_once_with(query)
        mock_qdrant_client.return_value.search.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_search_with_filters(self, qdrant_service, mock_qdrant_client, mock_azure_service):
        """Test search with filter conditions"""
        # Mock search results
        mock_qdrant_client.return_value.search.return_value = []
        
        # Test with filters
        filter_conditions = {"document_type": "RFP", "title": "Test Document"}
        results = await qdrant_service.search_similar_content(
            "test query", 
            filter_conditions=filter_conditions
        )
        
        # Assertions
        assert isinstance(results, list)
        
        # Verify search was called with filter
        call_args = mock_qdrant_client.return_value.search.call_args
        assert call_args[1]["query_filter"] is not None
    
    def test_get_document_structure(self, qdrant_service, mock_qdrant_client):
        """Test document structure retrieval"""
        # Mock scroll results
        mock_point1 = Mock()
        mock_point1.payload = {
            "chunk_id": 0,
            "content": "First chunk content",
            "chunk_metadata": {"type": "header"},
            "title": "Test Document",
            "document_type": "RFP",
            "upload_date": "2024-01-01T00:00:00",
            "stored_at": "2024-01-01T00:00:00"
        }
        
        mock_point2 = Mock()
        mock_point2.payload = {
            "chunk_id": 1,
            "content": "Second chunk content",
            "chunk_metadata": {"type": "body"},
            "title": "Test Document",
            "document_type": "RFP",
            "upload_date": "2024-01-01T00:00:00",
            "stored_at": "2024-01-01T00:00:00"
        }
        
        mock_qdrant_client.return_value.scroll.return_value = ([mock_point1, mock_point2], None)
        
        # Test
        document_id = "test_doc_1"
        structure = qdrant_service.get_document_structure(document_id)
        
        # Assertions
        assert structure["document_id"] == document_id
        assert len(structure["chunks"]) == 2
        assert structure["total_chunks"] == 2
        assert structure["metadata"]["title"] == "Test Document"
        assert structure["metadata"]["document_type"] == "RFP"
        
        # Check chunks are sorted by chunk_id
        assert structure["chunks"][0]["chunk_id"] == 0
        assert structure["chunks"][1]["chunk_id"] == 1
    
    def test_get_document_structure_not_found(self, qdrant_service, mock_qdrant_client):
        """Test document structure retrieval when document not found"""
        # Mock empty scroll results
        mock_qdrant_client.return_value.scroll.return_value = ([], None)
        
        # Test
        structure = qdrant_service.get_document_structure("nonexistent_doc")
        
        # Assertions
        assert structure == {}
    
    def test_delete_document(self, qdrant_service, mock_qdrant_client):
        """Test document deletion"""
        # Mock successful deletion
        mock_qdrant_client.return_value.delete.return_value = True
        
        # Test
        result = qdrant_service.delete_document("test_doc_1")
        
        # Assertions
        assert result is True
        mock_qdrant_client.return_value.delete.assert_called_once()
    
    def test_get_collection_info(self, qdrant_service, mock_qdrant_client):
        """Test collection information retrieval"""
        # Mock collection info
        mock_info = Mock()
        mock_info.config.params.vectors.size = 3072
        mock_info.vectors_count = 100
        mock_info.points_count = 50
        mock_info.status = "green"
        
        mock_qdrant_client.return_value.get_collection.return_value = mock_info
        
        # Test
        info = qdrant_service.get_collection_info()
        
        # Assertions
        assert info["vectors_count"] == 100
        assert info["points_count"] == 50
        assert info["status"] == "green"
    
    @pytest.mark.asyncio
    async def test_store_document_error_handling(self, qdrant_service, mock_qdrant_client, mock_azure_service):
        """Test error handling in document storage"""
        # Mock exception in embeddings
        mock_azure_service.get_embeddings.side_effect = Exception("Embedding error")
        
        # Test
        result = await qdrant_service.store_document("test_doc", "content", {})
        
        # Assertions
        assert result is False
    
    @pytest.mark.asyncio
    async def test_search_error_handling(self, qdrant_service, mock_qdrant_client, mock_azure_service):
        """Test error handling in search"""
        # Mock exception in search
        mock_qdrant_client.return_value.search.side_effect = Exception("Search error")
        
        # Test
        results = await qdrant_service.search_similar_content("test query")
        
        # Assertions
        assert results == []
    
    def test_collection_creation(self, mock_qdrant_client):
        """Test collection creation when it doesn't exist"""
        # Mock collections without our target collection
        mock_collections = Mock()
        mock_collections.collections = [Mock(name='other_collection')]
        mock_qdrant_client.return_value.get_collections.return_value = mock_collections
        
        # Test
        with patch.dict(os.environ, {
            'QDRANT_HOST': 'localhost',
            'QDRANT_PORT': '6333',
            'QDRANT_COLLECTION_NAME': 'test_collection'
        }):
            service = QdrantService()
        
        # Assertions
        mock_qdrant_client.return_value.create_collection.assert_called_once()
    
    def test_initialization_error_handling(self, mock_qdrant_client):
        """Test initialization error handling"""
        # Mock connection error
        mock_qdrant_client.side_effect = Exception("Connection error")
        
        # Test
        with pytest.raises(Exception) as exc_info:
            QdrantService()
        
        assert "Connection error" in str(exc_info.value)


if __name__ == "__main__":
    pytest.main([__file__])
