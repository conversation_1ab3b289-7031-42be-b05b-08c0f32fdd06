"""
Implementation Validation Script
Validates that all components of the RFP/RFO Document Processing System are correctly implemented
"""
import os
import sys
import importlib
import inspect
from typing import List, Dict, Any
import asyncio

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class ImplementationValidator:
    """Validates the complete implementation of the RFP/RFO Document Processing System"""
    
    def __init__(self):
        self.validation_results = {}
        self.errors = []
        self.warnings = []
    
    def validate_project_structure(self) -> bool:
        """Validate that all required files and directories exist"""
        print("🔍 Validating Project Structure...")
        
        required_files = [
            # Core services
            'services/__init__.py',
            'services/azure_openai_service.py',
            'services/qdrant_service.py', 
            'services/document_processor.py',
            
            # Multi-agent system
            'agents/document_parsing_agent.py',
            'agents/content_analysis_agent.py',
            'agents/prompt_generation_agent.py',
            'agents/quality_assurance_agent.py',
            
            # Workflows
            'workflows/document_processing_workflow.py',
            
            # UI
            'ui/document_processing_app.py',
            
            # Tests
            'tests/test_azure_openai_service.py',
            'tests/test_qdrant_service.py',
            'tests/test_document_processor.py',
            'tests/test_document_processing_workflow.py',
            'tests/test_integration.py',
            'tests/run_tests.py',
            
            # Configuration
            'config/settings.py',
            'requirements.txt',
            'docker-compose-qdrant.yml',
            
            # Documentation
            'DOCUMENT_PROCESSING_README.md',
            'SETUP_GUIDE.md',
            'IMPLEMENTATION_SUMMARY.md',
            'demo_script.py'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.errors.extend([f"Missing file: {f}" for f in missing_files])
            return False
        
        print("✅ All required files present")
        return True
    
    def validate_imports(self) -> bool:
        """Validate that all modules can be imported successfully"""
        print("📦 Validating Module Imports...")
        
        modules_to_test = [
            'services.azure_openai_service',
            'services.qdrant_service',
            'services.document_processor',
            'agents.document_parsing_agent',
            'agents.content_analysis_agent', 
            'agents.prompt_generation_agent',
            'agents.quality_assurance_agent',
            'workflows.document_processing_workflow',
            'config.settings'
        ]
        
        failed_imports = []
        for module_name in modules_to_test:
            try:
                importlib.import_module(module_name)
                print(f"✅ {module_name}")
            except ImportError as e:
                failed_imports.append(f"{module_name}: {str(e)}")
                print(f"❌ {module_name}: {str(e)}")
        
        if failed_imports:
            self.errors.extend(failed_imports)
            return False
        
        return True
    
    def validate_azure_openai_service(self) -> bool:
        """Validate Azure OpenAI service implementation"""
        print("🔧 Validating Azure OpenAI Service...")
        
        try:
            from services.azure_openai_service import AzureOpenAIService
            
            # Check required methods
            required_methods = [
                'get_embeddings',
                'get_single_embedding', 
                'chat_completion',
                'analyze_document_structure',
                'generate_contextual_prompt'
            ]
            
            for method_name in required_methods:
                if not hasattr(AzureOpenAIService, method_name):
                    self.errors.append(f"AzureOpenAIService missing method: {method_name}")
                    return False
                
                method = getattr(AzureOpenAIService, method_name)
                if not callable(method):
                    self.errors.append(f"AzureOpenAIService.{method_name} is not callable")
                    return False
            
            print("✅ Azure OpenAI Service implementation valid")
            return True
            
        except Exception as e:
            self.errors.append(f"Azure OpenAI Service validation failed: {str(e)}")
            return False
    
    def validate_qdrant_service(self) -> bool:
        """Validate Qdrant service implementation"""
        print("🗄️ Validating Qdrant Service...")
        
        try:
            from services.qdrant_service import QdrantService
            
            # Check required methods
            required_methods = [
                'store_document',
                'search_similar_content',
                'get_document_structure',
                'delete_document',
                'get_collection_info'
            ]
            
            for method_name in required_methods:
                if not hasattr(QdrantService, method_name):
                    self.errors.append(f"QdrantService missing method: {method_name}")
                    return False
            
            print("✅ Qdrant Service implementation valid")
            return True
            
        except Exception as e:
            self.errors.append(f"Qdrant Service validation failed: {str(e)}")
            return False
    
    def validate_document_processor(self) -> bool:
        """Validate document processor implementation"""
        print("📄 Validating Document Processor...")
        
        try:
            from services.document_processor import DocumentProcessor, DocumentStructure
            
            # Check required methods
            required_methods = [
                'process_uploaded_file',
                '_extract_pdf_content',
                '_extract_docx_content',
                '_extract_text_content',
                '_analyze_document_structure',
                'generate_section_prompts'
            ]
            
            for method_name in required_methods:
                if not hasattr(DocumentProcessor, method_name):
                    self.errors.append(f"DocumentProcessor missing method: {method_name}")
                    return False
            
            # Check DocumentStructure class
            if not hasattr(DocumentStructure, '__init__'):
                self.errors.append("DocumentStructure class not properly defined")
                return False
            
            print("✅ Document Processor implementation valid")
            return True
            
        except Exception as e:
            self.errors.append(f"Document Processor validation failed: {str(e)}")
            return False
    
    def validate_agents(self) -> bool:
        """Validate all agent implementations"""
        print("🤖 Validating Multi-Agent System...")
        
        agents_to_validate = [
            ('agents.document_parsing_agent', 'DocumentParsingAgent'),
            ('agents.content_analysis_agent', 'ContentAnalysisAgent'),
            ('agents.prompt_generation_agent', 'PromptGenerationAgent'),
            ('agents.quality_assurance_agent', 'QualityAssuranceAgent')
        ]
        
        for module_name, class_name in agents_to_validate:
            try:
                module = importlib.import_module(module_name)
                agent_class = getattr(module, class_name)
                
                # Check required methods
                required_methods = ['get_agent_status']
                for method_name in required_methods:
                    if not hasattr(agent_class, method_name):
                        self.errors.append(f"{class_name} missing method: {method_name}")
                        return False
                
                print(f"✅ {class_name} implementation valid")
                
            except Exception as e:
                self.errors.append(f"{class_name} validation failed: {str(e)}")
                return False
        
        return True
    
    def validate_workflow(self) -> bool:
        """Validate workflow implementation"""
        print("🔄 Validating Document Processing Workflow...")
        
        try:
            from workflows.document_processing_workflow import DocumentProcessingWorkflow
            
            # Check required methods
            required_methods = [
                'execute',
                'get_workflow_status',
                '_parse_document_step',
                '_analyze_content_step',
                '_generate_prompts_step',
                '_validate_quality_step',
                '_finalize_results_step'
            ]
            
            for method_name in required_methods:
                if not hasattr(DocumentProcessingWorkflow, method_name):
                    self.errors.append(f"DocumentProcessingWorkflow missing method: {method_name}")
                    return False
            
            print("✅ Document Processing Workflow implementation valid")
            return True
            
        except Exception as e:
            self.errors.append(f"Workflow validation failed: {str(e)}")
            return False
    
    def validate_ui_components(self) -> bool:
        """Validate UI implementation"""
        print("🖥️ Validating UI Components...")
        
        try:
            # Check if UI file exists and has required functions
            ui_file = 'ui/document_processing_app.py'
            if not os.path.exists(ui_file):
                self.errors.append("Enhanced UI file missing")
                return False

            # Read UI file and check for key functions
            with open(ui_file, 'r', encoding='utf-8') as f:
                ui_content = f.read()
            
            required_functions = [
                'document_upload_page',
                'document_library_page', 
                'processing_results_page',
                'prompt_editor_page',
                'system_status_page'
            ]
            
            for func_name in required_functions:
                if f"def {func_name}" not in ui_content:
                    self.errors.append(f"UI missing function: {func_name}")
                    return False
            
            print("✅ UI Components implementation valid")
            return True
            
        except Exception as e:
            self.errors.append(f"UI validation failed: {str(e)}")
            return False
    
    def validate_tests(self) -> bool:
        """Validate test implementation"""
        print("🧪 Validating Test Suite...")
        
        test_files = [
            'tests/test_azure_openai_service.py',
            'tests/test_qdrant_service.py',
            'tests/test_document_processor.py', 
            'tests/test_document_processing_workflow.py',
            'tests/test_integration.py'
        ]
        
        for test_file in test_files:
            if not os.path.exists(test_file):
                self.errors.append(f"Missing test file: {test_file}")
                return False
            
            # Check if test file has test classes
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'class Test' not in content and 'def test_' not in content:
                    self.warnings.append(f"Test file may not contain proper tests: {test_file}")
        
        print("✅ Test Suite implementation valid")
        return True
    
    def validate_configuration(self) -> bool:
        """Validate configuration implementation"""
        print("⚙️ Validating Configuration...")
        
        try:
            from config.settings import (
                AZURE_OPENAI_ENDPOINT,
                AZURE_OPENAI_API_KEY, 
                AZURE_OPENAI_DEPLOYMENT_NAME,
                EMBEDDING_MODEL,
                QDRANT_HOST,
                QDRANT_PORT,
                QDRANT_COLLECTION_NAME
            )
            
            print("✅ Configuration implementation valid")
            return True
            
        except ImportError as e:
            self.errors.append(f"Configuration validation failed: {str(e)}")
            return False
    
    def validate_documentation(self) -> bool:
        """Validate documentation completeness"""
        print("📚 Validating Documentation...")
        
        required_docs = [
            'DOCUMENT_PROCESSING_README.md',
            'SETUP_GUIDE.md', 
            'IMPLEMENTATION_SUMMARY.md'
        ]
        
        for doc_file in required_docs:
            if not os.path.exists(doc_file):
                self.errors.append(f"Missing documentation: {doc_file}")
                return False
            
            # Check if documentation has substantial content
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if len(content) < 1000:  # Minimum content check
                    self.warnings.append(f"Documentation may be incomplete: {doc_file}")
        
        print("✅ Documentation implementation valid")
        return True
    
    async def run_validation(self) -> Dict[str, Any]:
        """Run complete validation suite"""
        print("🔍 Starting Implementation Validation")
        print("=" * 50)
        
        validation_steps = [
            ("Project Structure", self.validate_project_structure),
            ("Module Imports", self.validate_imports),
            ("Azure OpenAI Service", self.validate_azure_openai_service),
            ("Qdrant Service", self.validate_qdrant_service),
            ("Document Processor", self.validate_document_processor),
            ("Multi-Agent System", self.validate_agents),
            ("Workflow", self.validate_workflow),
            ("UI Components", self.validate_ui_components),
            ("Test Suite", self.validate_tests),
            ("Configuration", self.validate_configuration),
            ("Documentation", self.validate_documentation)
        ]
        
        results = {}
        for step_name, validation_func in validation_steps:
            try:
                results[step_name] = validation_func()
            except Exception as e:
                results[step_name] = False
                self.errors.append(f"{step_name} validation crashed: {str(e)}")
        
        # Generate summary
        total_steps = len(validation_steps)
        passed_steps = sum(1 for result in results.values() if result)
        
        print("\n" + "=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        print(f"Total Validation Steps: {total_steps}")
        print(f"Passed: {passed_steps} ✅")
        print(f"Failed: {total_steps - passed_steps} ❌")
        print(f"Success Rate: {(passed_steps/total_steps)*100:.1f}%")
        
        if self.errors:
            print(f"\n❌ Errors Found ({len(self.errors)}):")
            for error in self.errors:
                print(f"  • {error}")
        
        if self.warnings:
            print(f"\n⚠️ Warnings ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  • {warning}")
        
        if passed_steps == total_steps:
            print("\n🎉 ALL VALIDATIONS PASSED!")
            print("The RFP/RFO Document Processing System is fully implemented and ready for use.")
        else:
            print(f"\n💥 {total_steps - passed_steps} VALIDATION(S) FAILED")
            print("Please address the errors above before proceeding.")
        
        return {
            'total_steps': total_steps,
            'passed_steps': passed_steps,
            'failed_steps': total_steps - passed_steps,
            'success_rate': (passed_steps/total_steps)*100,
            'errors': self.errors,
            'warnings': self.warnings,
            'results': results
        }


async def main():
    """Main validation function"""
    validator = ImplementationValidator()
    results = await validator.run_validation()
    
    # Return appropriate exit code
    return 0 if results['failed_steps'] == 0 else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Validation failed: {str(e)}")
        sys.exit(1)
