import logging
from typing import Optional

from openai import AzureOpenAI

logger = logging.getLogger(__name__)


class LLMClient:
    """
    Azure OpenAI API client class
    """

    def __init__(self, azure_endpoint: str, api_key: str, model: str, api_version: str = "2024-02-01"):
        """
        Initialize Azure OpenAI API client
        :param azure_endpoint: Azure OpenAI endpoint URL
        :param api_key: Azure OpenAI API key
        :param model: Name of the model deployment to use
        :param api_version: API version for Azure OpenAI
        """
        self.azure_endpoint = azure_endpoint
        self.api_key = api_key
        self.model = model
        self.api_version = api_version
        self.client = AzureOpenAI(
            azure_endpoint=azure_endpoint,
            api_key=api_key,
            api_version=api_version
        )

    def completion(
        self,
        user_message: str,
        system_prompt: Optional[str] = None,
        image_paths: Optional[list[str]] = None,
        temperature: float = 0.7,
        max_tokens: int = 8192,
    ) -> str:
        """
        Create chat dialogue (supports multimodal)

        Args:
            user_message: User message content
            system_prompt: System prompt (optional)
            image_paths: List of image paths (optional)
            temperature: Generation temperature
            max_tokens: Maximum number of tokens

        Returns:
            str: Model generated response content
        """
        # Create the message content
        user_content = [{"type": "text", "text": user_message}]
        if image_paths:
            for img_path in image_paths:
                base64_image = self.encode_image(img_path)
                user_content.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                    }
                )

        messages = []
        if system_prompt:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content},
            ]
        else:
            messages = [{"role": "user", "content": user_content}]

        try:
            logger.info(f"Calling Azure OpenAI model: {self.model}")
            if image_paths:
                logger.info(f"Processing {len(image_paths)} image(s)")
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=120,  # 2 minute timeout
                extra_headers={
                    "X-Title": "MarkPDFdown",
                    "HTTP-Referer": "https://github.com/MarkPDFdown/markpdfdown.git",
                },
            )
            
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content
                logger.info(f"Received response with {len(content) if content else 0} characters")
                return content
            else:
                logger.error("No response choices returned from Azure OpenAI")
                return ""

        except Exception as e:
            logger.error(f"Azure OpenAI API request failed: {str(e)}")
            logger.error(f"Model: {self.model}, Endpoint: {self.azure_endpoint}")
            raise e

    def encode_image(self, image_path: str) -> str:
        import base64

        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")
