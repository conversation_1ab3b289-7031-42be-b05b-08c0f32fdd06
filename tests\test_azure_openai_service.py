"""
Tests for Azure OpenAI Service
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.azure_openai_service import AzureOpenAIService


class TestAzureOpenAIService:
    """Test cases for Azure OpenAI Service"""
    
    @pytest.fixture
    def mock_azure_client(self):
        """Mock Azure OpenAI client"""
        with patch('services.azure_openai_service.AsyncAzureOpenAI') as mock_client:
            yield mock_client
    
    @pytest.fixture
    def azure_service(self, mock_azure_client):
        """Create Azure OpenAI service instance with mocked client"""
        with patch.dict(os.environ, {
            'AZURE_OPENAI_ENDPOINT': 'https://test.openai.azure.com/',
            'AZURE_OPENAI_API_KEY': 'test-key',
            'AZURE_OPENAI_DEPLOYMENT_NAME': 'test-deployment',
            'AZURE_OPENAI_API_VERSION': '2024-02-01'
        }):
            return AzureOpenAIService()
    
    @pytest.mark.asyncio
    async def test_get_embeddings_success(self, azure_service, mock_azure_client):
        """Test successful embedding generation"""
        # Mock response
        mock_response = Mock()
        mock_response.data = [
            Mock(embedding=[0.1, 0.2, 0.3]),
            Mock(embedding=[0.4, 0.5, 0.6])
        ]
        
        mock_azure_client.return_value.embeddings.create = AsyncMock(return_value=mock_response)
        
        # Test
        texts = ["test text 1", "test text 2"]
        embeddings = await azure_service.get_embeddings(texts)
        
        # Assertions
        assert len(embeddings) == 2
        assert embeddings[0] == [0.1, 0.2, 0.3]
        assert embeddings[1] == [0.4, 0.5, 0.6]
        
        # Verify API call
        mock_azure_client.return_value.embeddings.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_single_embedding(self, azure_service, mock_azure_client):
        """Test single embedding generation"""
        # Mock response
        mock_response = Mock()
        mock_response.data = [Mock(embedding=[0.1, 0.2, 0.3])]
        
        mock_azure_client.return_value.embeddings.create = AsyncMock(return_value=mock_response)
        
        # Test
        embedding = await azure_service.get_single_embedding("test text")
        
        # Assertions
        assert embedding == [0.1, 0.2, 0.3]
    
    @pytest.mark.asyncio
    async def test_chat_completion_success(self, azure_service, mock_azure_client):
        """Test successful chat completion"""
        # Mock response
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content="Test response"))]
        
        mock_azure_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)
        
        # Test
        messages = [{"role": "user", "content": "Test message"}]
        response = await azure_service.chat_completion(messages)
        
        # Assertions
        assert response == "Test response"
        
        # Verify API call
        mock_azure_client.return_value.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_analyze_document_structure(self, azure_service, mock_azure_client):
        """Test document structure analysis"""
        # Mock response
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content='{"headings": [], "sections": [], "document_type": "RFP"}'))]
        
        mock_azure_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)
        
        # Test
        content = "Test document content"
        structure = await azure_service.analyze_document_structure(content)
        
        # Assertions
        assert isinstance(structure, dict)
        assert "headings" in structure
        assert "sections" in structure
        assert "document_type" in structure
    
    @pytest.mark.asyncio
    async def test_generate_contextual_prompt(self, azure_service, mock_azure_client):
        """Test contextual prompt generation"""
        # Mock response
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content="Generated prompt for the section"))]
        
        mock_azure_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)
        
        # Test
        prompt = await azure_service.generate_contextual_prompt(
            heading="Test Section",
            context="Test context",
            document_type="RFP"
        )
        
        # Assertions
        assert prompt == "Generated prompt for the section"
    
    @pytest.mark.asyncio
    async def test_embeddings_error_handling(self, azure_service, mock_azure_client):
        """Test error handling in embeddings"""
        # Mock exception
        mock_azure_client.return_value.embeddings.create = AsyncMock(side_effect=Exception("API Error"))
        
        # Test
        with pytest.raises(Exception) as exc_info:
            await azure_service.get_embeddings(["test"])
        
        assert "API Error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_chat_completion_error_handling(self, azure_service, mock_azure_client):
        """Test error handling in chat completion"""
        # Mock exception
        mock_azure_client.return_value.chat.completions.create = AsyncMock(side_effect=Exception("API Error"))
        
        # Test
        with pytest.raises(Exception) as exc_info:
            await azure_service.chat_completion([{"role": "user", "content": "test"}])
        
        assert "API Error" in str(exc_info.value)
    
    def test_initialization_without_credentials(self):
        """Test initialization without proper credentials"""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError) as exc_info:
                AzureOpenAIService()
            
            assert "Azure OpenAI credentials not found" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_analyze_document_structure_json_error(self, azure_service, mock_azure_client):
        """Test document structure analysis with invalid JSON response"""
        # Mock response with invalid JSON
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content="Invalid JSON response"))]
        
        mock_azure_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)
        
        # Test
        structure = await azure_service.analyze_document_structure("test content")
        
        # Assertions - should return basic structure on JSON error
        assert isinstance(structure, dict)
        assert "analysis" in structure
        assert structure["analysis"] == "Invalid JSON response"
        assert structure["document_type"] == "unknown"


if __name__ == "__main__":
    pytest.main([__file__])
