"""
Enhanced Streamlit UI for RFP/RFO Document Processing System
"""
import streamlit as st
import tempfile
import os
import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List
import pandas as pd

# Add project root to path
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflows.document_processing_workflow import document_processing_workflow
from services.qdrant_service import qdrant_service
from services.azure_openai_service import azure_openai_service

# Set page config
st.set_page_config(
    page_title="RFP/RFO Document Processing System",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'processed_documents' not in st.session_state:
    st.session_state.processed_documents = []

if 'current_document' not in st.session_state:
    st.session_state.current_document = None

if 'processing_result' not in st.session_state:
    st.session_state.processing_result = None

if 'section_prompts' not in st.session_state:
    st.session_state.section_prompts = {}

if 'edited_prompts' not in st.session_state:
    st.session_state.edited_prompts = {}


def main():
    """Main application function"""
    st.title("🔍 RFP/RFO Document Processing System")
    st.markdown("""
    **Comprehensive AI-powered document processing with multi-agent analysis**
    
    Upload RFP/RFO documents to:
    - Extract and analyze document structure
    - Generate contextual prompts for each section
    - Perform content analysis and compliance checking
    - Create interactive editing interface
    """)
    
    # Sidebar navigation
    with st.sidebar:
        st.header("📋 Navigation")
        
        page = st.selectbox(
            "Select Page",
            ["Document Upload", "Document Library", "Processing Results", "Prompt Editor", "System Status"]
        )
    
    # Route to different pages
    if page == "Document Upload":
        document_upload_page()
    elif page == "Document Library":
        document_library_page()
    elif page == "Processing Results":
        processing_results_page()
    elif page == "Prompt Editor":
        prompt_editor_page()
    elif page == "System Status":
        system_status_page()


def document_upload_page():
    """Document upload and processing page"""
    st.header("📤 Document Upload & Processing")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Upload Document")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a document file",
            type=['pdf', 'docx', 'txt', 'md'],
            help="Supported formats: PDF, DOCX, TXT, MD"
        )
        
        # Document type selection
        document_type = st.selectbox(
            "Document Type",
            ["RFP", "RFO", "SOW", "RFI", "Other"],
            help="Select the type of document you're uploading"
        )
        
        # Processing options
        st.subheader("Processing Options")
        
        analysis_options = st.multiselect(
            "Analysis Types",
            ["Structure Analysis", "Content Analysis", "Compliance Check", "Requirements Extraction"],
            default=["Structure Analysis", "Content Analysis"],
            help="Select the types of analysis to perform"
        )
        
        generate_prompts = st.checkbox(
            "Generate Section Prompts",
            value=True,
            help="Automatically generate contextual prompts for each section"
        )
        
        # Process button
        if st.button("🚀 Process Document", type="primary"):
            if uploaded_file is not None:
                process_uploaded_document(uploaded_file, document_type, analysis_options, generate_prompts)
            else:
                st.error("Please upload a document first")
    
    with col2:
        st.subheader("Processing Status")
        
        if st.session_state.processing_result:
            result = st.session_state.processing_result
            
            # Display processing status
            if result.get('overall_status') == 'success':
                st.success("✅ Processing completed successfully!")
            else:
                st.error("❌ Processing failed")
                if result.get('error'):
                    st.error(f"Error: {result['error']}")
            
            # Display processing summary
            if result.get('processing_summary'):
                summary = result['processing_summary']
                
                st.metric("Sections Found", summary.get('sections_found', 0))
                st.metric("Headings Found", summary.get('headings_found', 0))
                st.metric("Prompts Generated", summary.get('prompts_generated', 0))
                
                quality_score = summary.get('quality_score', 0)
                st.metric("Quality Score", f"{quality_score:.2f}")
                
                if quality_score > 0.8:
                    st.success("High quality processing")
                elif quality_score > 0.6:
                    st.warning("Moderate quality processing")
                else:
                    st.error("Low quality processing - review recommended")


def process_uploaded_document(uploaded_file, document_type, analysis_options, generate_prompts):
    """Process the uploaded document"""
    try:
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            tmp_file_path = tmp_file.name
        
        # Show processing spinner
        with st.spinner("Processing document... This may take a few minutes."):
            # Execute the document processing workflow
            result = asyncio.run(
                document_processing_workflow.execute(
                    file_path=tmp_file_path,
                    file_name=uploaded_file.name,
                    document_type=document_type
                )
            )
            
            # Store results in session state
            st.session_state.processing_result = result
            st.session_state.current_document = {
                'name': uploaded_file.name,
                'type': document_type,
                'processed_at': datetime.now(),
                'result': result
            }
            
            # Add to document library
            st.session_state.processed_documents.append(st.session_state.current_document)
            
            # Extract section prompts if available
            if result.get('prompt_generation', {}).get('section_prompts'):
                st.session_state.section_prompts = result['prompt_generation']['section_prompts']
        
        # Clean up temporary file
        os.unlink(tmp_file_path)
        
        st.success("Document processed successfully!")
        st.rerun()
        
    except Exception as e:
        st.error(f"Error processing document: {str(e)}")
        # Clean up temporary file on error
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass


def document_library_page():
    """Document library page showing processed documents"""
    st.header("📚 Document Library")
    
    if not st.session_state.processed_documents:
        st.info("No documents processed yet. Upload a document to get started.")
        return
    
    # Display processed documents
    for i, doc in enumerate(st.session_state.processed_documents):
        with st.expander(f"📄 {doc['name']} ({doc['type']}) - {doc['processed_at'].strftime('%Y-%m-%d %H:%M')}"):
            
            col1, col2, col3 = st.columns([2, 2, 1])
            
            with col1:
                st.write(f"**Document Type:** {doc['type']}")
                st.write(f"**Processed:** {doc['processed_at'].strftime('%Y-%m-%d %H:%M:%S')}")
                
                result = doc['result']
                if result.get('overall_status') == 'success':
                    st.success("✅ Successfully processed")
                else:
                    st.error("❌ Processing failed")
            
            with col2:
                # Display processing metrics
                summary = result.get('processing_summary', {})
                
                metrics_col1, metrics_col2 = st.columns(2)
                with metrics_col1:
                    st.metric("Sections", summary.get('sections_found', 0))
                    st.metric("Headings", summary.get('headings_found', 0))
                
                with metrics_col2:
                    st.metric("Prompts", summary.get('prompts_generated', 0))
                    st.metric("Quality", f"{summary.get('quality_score', 0):.2f}")
            
            with col3:
                if st.button(f"View Details", key=f"view_{i}"):
                    st.session_state.current_document = doc
                    st.session_state.processing_result = doc['result']
                    st.rerun()
                
                if st.button(f"Delete", key=f"delete_{i}"):
                    st.session_state.processed_documents.pop(i)
                    st.rerun()


def processing_results_page():
    """Display detailed processing results"""
    st.header("📊 Processing Results")
    
    if not st.session_state.processing_result:
        st.info("No processing results available. Process a document first.")
        return
    
    result = st.session_state.processing_result
    
    # Document information
    doc_info = result.get('document_info', {})
    st.subheader(f"📄 {doc_info.get('file_name', 'Unknown Document')}")
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Document Type", doc_info.get('document_type', 'Unknown'))
    with col2:
        st.metric("Processing Status", result.get('overall_status', 'Unknown'))
    with col3:
        st.metric("Processed At", doc_info.get('processed_at', 'Unknown')[:19])
    
    # Create tabs for different result sections
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📋 Document Structure", 
        "🔍 Content Analysis", 
        "💡 Generated Prompts", 
        "✅ Quality Report",
        "📊 Raw Data"
    ])
    
    with tab1:
        display_document_structure(result)
    
    with tab2:
        display_content_analysis(result)
    
    with tab3:
        display_generated_prompts(result)
    
    with tab4:
        display_quality_report(result)
    
    with tab5:
        display_raw_data(result)


def display_document_structure(result):
    """Display document structure analysis"""
    st.subheader("📋 Document Structure")
    
    parsing_result = result.get('parsing_result', {})
    structure = parsing_result.get('structure', {})
    
    if not structure:
        st.warning("No structure information available")
        return
    
    # Display headings
    headings = structure.get('headings', [])
    if headings:
        st.write("**Document Headings:**")
        
        headings_data = []
        for heading in headings:
            headings_data.append({
                'Level': heading.get('level', 0),
                'Title': heading.get('title', ''),
                'Type': heading.get('type', ''),
                'Line': heading.get('line_number', 0)
            })
        
        df_headings = pd.DataFrame(headings_data)
        st.dataframe(df_headings, use_container_width=True)
    
    # Display table of contents
    toc = structure.get('table_of_contents', [])
    if toc:
        st.write("**Table of Contents:**")
        for item in toc:
            st.write(f"{item.get('formatted', '')}")
    
    # Display sections summary
    sections = structure.get('sections', [])
    if sections:
        st.write(f"**Sections Summary:** {len(sections)} sections found")
        
        sections_data = []
        for i, section in enumerate(sections):
            heading = section.get('heading', {})
            sections_data.append({
                'Section': heading.get('title', f'Section {i+1}'),
                'Level': heading.get('level', 0),
                'Word Count': section.get('word_count', 0),
                'Lines': f"{section.get('start_line', 0)}-{section.get('end_line', 0)}"
            })
        
        df_sections = pd.DataFrame(sections_data)
        st.dataframe(df_sections, use_container_width=True)


def display_content_analysis(result):
    """Display content analysis results"""
    st.subheader("🔍 Content Analysis")
    
    content_analysis = result.get('content_analysis', {})
    analysis_result = content_analysis.get('analysis_result', {})
    
    if not analysis_result:
        st.warning("No content analysis available")
        return
    
    # Executive summary
    if analysis_result.get('executive_summary'):
        st.write("**Executive Summary:**")
        st.write(analysis_result['executive_summary'])
    
    # Key requirements
    key_requirements = analysis_result.get('key_requirements', [])
    if key_requirements:
        st.write("**Key Requirements Found:**")
        
        req_data = []
        for req in key_requirements[:10]:  # Show top 10
            req_data.append({
                'Pattern': req.get('pattern', ''),
                'Context': req.get('context', '')[:100] + "..." if len(req.get('context', '')) > 100 else req.get('context', ''),
                'Position': req.get('position', 0)
            })
        
        df_requirements = pd.DataFrame(req_data)
        st.dataframe(df_requirements, use_container_width=True)
    
    # Compliance elements
    compliance_elements = analysis_result.get('compliance_elements', [])
    if compliance_elements:
        st.write("**Compliance Elements:**")
        
        compliance_data = []
        for comp in compliance_elements[:10]:  # Show top 10
            compliance_data.append({
                'Keyword': comp.get('keyword', ''),
                'Context': comp.get('context', '')[:100] + "..." if len(comp.get('context', '')) > 100 else comp.get('context', ''),
                'Position': comp.get('position', 0)
            })
        
        df_compliance = pd.DataFrame(compliance_data)
        st.dataframe(df_compliance, use_container_width=True)
    
    # Content quality score
    quality_score = analysis_result.get('content_quality_score', 0)
    st.metric("Content Quality Score", f"{quality_score:.2f}")


def display_generated_prompts(result):
    """Display generated prompts with editing capability"""
    st.subheader("💡 Generated Prompts")
    
    prompt_generation = result.get('prompt_generation', {})
    section_prompts = prompt_generation.get('section_prompts', {})
    
    if not section_prompts:
        st.warning("No prompts generated")
        return
    
    st.write(f"**Total Prompts Generated:** {len(section_prompts)}")
    
    # Display prompts with editing capability
    for section_name, prompt_data in section_prompts.items():
        with st.expander(f"📝 {section_name}"):
            
            col1, col2 = st.columns([3, 1])
            
            with col1:
                # Show original section content (truncated)
                section_content = prompt_data.get('section_content', '')
                if section_content:
                    st.write("**Original Section Content:**")
                    st.text_area(
                        "Content",
                        value=section_content,
                        height=100,
                        disabled=True,
                        key=f"content_{section_name}"
                    )
            
            with col2:
                # Auto-generate button
                if st.button(f"🔄 Regenerate", key=f"regen_{section_name}"):
                    regenerate_prompt(section_name, section_content)
            
            # Editable prompt
            original_prompt = prompt_data.get('prompt', '')
            
            # Get edited prompt from session state or use original
            current_prompt = st.session_state.edited_prompts.get(section_name, original_prompt)
            
            edited_prompt = st.text_area(
                "Generated Prompt (Editable)",
                value=current_prompt,
                height=200,
                key=f"prompt_{section_name}",
                help="You can edit this prompt to customize it for your needs"
            )
            
            # Save edited prompt to session state
            if edited_prompt != current_prompt:
                st.session_state.edited_prompts[section_name] = edited_prompt
            
            # Show prompt metadata
            st.caption(f"Generated at: {prompt_data.get('generated_at', 'Unknown')}")


def regenerate_prompt(section_name: str, section_content: str):
    """Regenerate a prompt for a specific section"""
    try:
        with st.spinner(f"Regenerating prompt for {section_name}..."):
            # Use the prompt generation agent to create a new prompt
            new_prompt = asyncio.run(
                azure_openai_service.generate_contextual_prompt(
                    heading=section_name,
                    context=section_content,
                    document_type="RFP"
                )
            )
            
            # Update the session state
            st.session_state.edited_prompts[section_name] = new_prompt
            
        st.success(f"Prompt regenerated for {section_name}")
        st.rerun()
        
    except Exception as e:
        st.error(f"Error regenerating prompt: {str(e)}")


def display_quality_report(result):
    """Display quality assurance report"""
    st.subheader("✅ Quality Assurance Report")
    
    quality_validation = result.get('quality_validation', {})
    validation_report = quality_validation.get('validation_report', {})
    
    if not validation_report:
        st.warning("No quality report available")
        return
    
    # Overall quality score
    overall_score = validation_report.get('overall_quality_score', 0)
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Overall Quality Score", f"{overall_score:.2f}")
    with col2:
        passed_checks = len(validation_report.get('passed_checks', []))
        st.metric("Passed Checks", passed_checks)
    with col3:
        failed_checks = len(validation_report.get('failed_checks', []))
        st.metric("Failed Checks", failed_checks)
    
    # Quality status
    if overall_score > 0.8:
        st.success("🟢 High Quality Processing")
    elif overall_score > 0.6:
        st.warning("🟡 Moderate Quality Processing")
    else:
        st.error("🔴 Low Quality Processing")
    
    # Detailed validation results
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**✅ Passed Checks:**")
        for check in validation_report.get('passed_checks', []):
            st.write(f"• {check}")
    
    with col2:
        st.write("**❌ Failed Checks:**")
        for check in validation_report.get('failed_checks', []):
            st.write(f"• {check}")
    
    # Issues and recommendations
    issues = validation_report.get('issues_found', [])
    if issues:
        st.write("**⚠️ Issues Found:**")
        for issue in issues:
            st.warning(f"• {issue}")
    
    recommendations = validation_report.get('recommendations', [])
    if recommendations:
        st.write("**💡 Recommendations:**")
        for rec in recommendations:
            st.info(f"• {rec}")


def display_raw_data(result):
    """Display raw processing data"""
    st.subheader("📊 Raw Processing Data")
    
    st.write("**Complete Processing Results:**")
    st.json(result)


def prompt_editor_page():
    """Prompt editor page for customizing prompts"""
    st.header("✏️ Prompt Editor")
    
    if not st.session_state.section_prompts:
        st.info("No prompts available. Process a document first to generate prompts.")
        return
    
    st.write("Edit and customize the generated prompts for your specific needs.")
    
    # Prompt management
    col1, col2 = st.columns([3, 1])
    
    with col1:
        # Select section to edit
        section_names = list(st.session_state.section_prompts.keys())
        selected_section = st.selectbox("Select Section to Edit", section_names)
    
    with col2:
        # Export prompts
        if st.button("📥 Export All Prompts"):
            export_prompts()
        
        # Import prompts
        uploaded_prompts = st.file_uploader("📤 Import Prompts", type=['json'])
        if uploaded_prompts:
            import_prompts(uploaded_prompts)
    
    if selected_section:
        # Edit selected prompt
        st.subheader(f"Editing: {selected_section}")
        
        original_prompt = st.session_state.section_prompts[selected_section].get('prompt', '')
        current_prompt = st.session_state.edited_prompts.get(selected_section, original_prompt)
        
        # Prompt editing interface
        col1, col2 = st.columns([2, 1])
        
        with col1:
            edited_prompt = st.text_area(
                "Prompt Content",
                value=current_prompt,
                height=400,
                key=f"editor_{selected_section}"
            )
            
            # Save changes
            if st.button("💾 Save Changes"):
                st.session_state.edited_prompts[selected_section] = edited_prompt
                st.success("Prompt saved successfully!")
        
        with col2:
            st.write("**Prompt Guidelines:**")
            st.write("• Be specific and actionable")
            st.write("• Include clear instructions")
            st.write("• Specify desired format")
            st.write("• Include examples if helpful")
            st.write("• Consider compliance requirements")
            
            # Prompt optimization
            if st.button("🔧 Optimize Prompt"):
                optimize_prompt(selected_section, edited_prompt)
            
            # Reset to original
            if st.button("🔄 Reset to Original"):
                if selected_section in st.session_state.edited_prompts:
                    del st.session_state.edited_prompts[selected_section]
                st.rerun()


def optimize_prompt(section_name: str, current_prompt: str):
    """Optimize a prompt using AI"""
    try:
        with st.spinner("Optimizing prompt..."):
            # Get section content for context
            section_content = st.session_state.section_prompts[section_name].get('section_content', '')
            
            # Use the prompt generation agent to optimize
            from agents.prompt_generation_agent import PromptGenerationAgent
            prompt_agent = PromptGenerationAgent()
            
            optimization_result = asyncio.run(
                prompt_agent.optimize_prompt(
                    original_prompt=current_prompt,
                    section_context=section_content,
                    optimization_goals=["clarity", "specificity", "completeness"]
                )
            )
            
            if optimization_result.get('status') == 'success':
                # Show optimization results
                st.success("Prompt optimized successfully!")
                st.write("**Optimization Results:**")
                st.write(optimization_result['optimization_result'])
            else:
                st.error("Optimization failed")
                
    except Exception as e:
        st.error(f"Error optimizing prompt: {str(e)}")


def export_prompts():
    """Export all prompts to JSON"""
    try:
        # Combine original and edited prompts
        all_prompts = {}
        
        for section_name, prompt_data in st.session_state.section_prompts.items():
            current_prompt = st.session_state.edited_prompts.get(
                section_name, 
                prompt_data.get('prompt', '')
            )
            
            all_prompts[section_name] = {
                'prompt': current_prompt,
                'section_content': prompt_data.get('section_content', ''),
                'generated_at': prompt_data.get('generated_at', ''),
                'edited': section_name in st.session_state.edited_prompts
            }
        
        # Create download
        prompts_json = json.dumps(all_prompts, indent=2)
        
        st.download_button(
            label="📥 Download Prompts JSON",
            data=prompts_json,
            file_name=f"prompts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )
        
    except Exception as e:
        st.error(f"Error exporting prompts: {str(e)}")


def import_prompts(uploaded_file):
    """Import prompts from JSON file"""
    try:
        prompts_data = json.loads(uploaded_file.getvalue().decode('utf-8'))
        
        # Update session state with imported prompts
        for section_name, prompt_info in prompts_data.items():
            if isinstance(prompt_info, dict) and 'prompt' in prompt_info:
                st.session_state.edited_prompts[section_name] = prompt_info['prompt']
            elif isinstance(prompt_info, str):
                st.session_state.edited_prompts[section_name] = prompt_info
        
        st.success(f"Imported {len(prompts_data)} prompts successfully!")
        st.rerun()
        
    except Exception as e:
        st.error(f"Error importing prompts: {str(e)}")


def system_status_page():
    """System status and configuration page"""
    st.header("⚙️ System Status")
    
    # Workflow status
    st.subheader("🔄 Workflow Status")
    
    try:
        workflow_status = document_processing_workflow.get_workflow_status()
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**Workflow:** {workflow_status['workflow_name']}")
            st.write(f"**Status:** {workflow_status['status']}")
            st.write(f"**Version:** {workflow_status['version']}")
        
        with col2:
            st.write("**Processing Steps:**")
            for step in workflow_status['processing_steps']:
                st.write(f"• {step}")
        
        # Agent status
        st.subheader("🤖 Agent Status")
        
        agents = workflow_status.get('agents', [])
        for agent in agents:
            with st.expander(f"🤖 {agent['agent_name']}"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Status:** {agent['status']}")
                    st.write(f"**Version:** {agent['version']}")
                
                with col2:
                    st.write("**Capabilities:**")
                    for capability in agent['capabilities']:
                        st.write(f"• {capability}")
        
    except Exception as e:
        st.error(f"Error getting workflow status: {str(e)}")
    
    # Database status
    st.subheader("🗄️ Database Status")
    
    try:
        # Check Qdrant status
        collection_info = qdrant_service.get_collection_info()
        
        if collection_info:
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Vectors Count", collection_info.get('vectors_count', 0))
            with col2:
                st.metric("Points Count", collection_info.get('points_count', 0))
            with col3:
                st.metric("Status", collection_info.get('status', 'Unknown'))
        else:
            st.warning("Unable to connect to Qdrant database")
            
    except Exception as e:
        st.error(f"Error checking database status: {str(e)}")
    
    # Configuration
    st.subheader("⚙️ Configuration")
    
    with st.expander("🔧 System Configuration"):
        st.write("**Azure OpenAI Configuration:**")
        from config.settings import AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
        st.write(f"• Endpoint: {AZURE_OPENAI_ENDPOINT}")
        st.write(f"• Deployment: {AZURE_OPENAI_DEPLOYMENT_NAME}")
        
        st.write("**Qdrant Configuration:**")
        from config.settings import QDRANT_HOST, QDRANT_PORT, QDRANT_COLLECTION_NAME
        st.write(f"• Host: {QDRANT_HOST}")
        st.write(f"• Port: {QDRANT_PORT}")
        st.write(f"• Collection: {QDRANT_COLLECTION_NAME}")


if __name__ == "__main__":
    main()
