"""
Tests for Document Processor
"""
import pytest
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.document_processor import DocumentProcessor, DocumentStructure


class TestDocumentProcessor:
    """Test cases for Document Processor"""
    
    @pytest.fixture
    def document_processor(self):
        """Create document processor instance"""
        return DocumentProcessor()
    
    @pytest.fixture
    def mock_azure_service(self):
        """Mock Azure OpenAI service"""
        with patch('services.document_processor.azure_openai_service') as mock_service:
            mock_service.analyze_document_structure = AsyncMock(return_value={
                'document_type': 'RFP',
                'headings': [],
                'sections': []
            })
            mock_service.generate_contextual_prompt = AsyncMock(return_value="Generated prompt")
            yield mock_service
    
    @pytest.fixture
    def mock_qdrant_service(self):
        """Mock Qdrant service"""
        with patch('services.document_processor.qdrant_service') as mock_service:
            mock_service.store_document = AsyncMock(return_value=True)
            mock_service.get_document_structure = Mock(return_value={
                'metadata': {'document_type': 'RFP'},
                'chunks': []
            })
            yield mock_service
    
    @pytest.fixture
    def sample_pdf_file(self):
        """Create a sample PDF file for testing"""
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            # Write some dummy PDF content
            tmp_file.write(b'%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n')
            tmp_file_path = tmp_file.name
        
        yield tmp_file_path
        
        # Cleanup
        os.unlink(tmp_file_path)
    
    @pytest.fixture
    def sample_text_file(self):
        """Create a sample text file for testing"""
        content = """# Main Heading
        
This is the introduction section.

## Section 1: Requirements
- Requirement 1
- Requirement 2

## Section 2: Timeline
The project timeline is 6 months.

### 2.1 Phase 1
Initial phase details.

### 2.2 Phase 2
Second phase details.
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        yield tmp_file_path, content
        
        # Cleanup
        os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_process_text_file(self, document_processor, sample_text_file, mock_azure_service, mock_qdrant_service):
        """Test processing a text file"""
        file_path, expected_content = sample_text_file
        
        # Test
        result = await document_processor.process_uploaded_file(
            file_path=file_path,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['content'] == expected_content
        assert result['metadata']['title'] == "test.txt"
        assert result['metadata']['document_type'] == "RFP"
        assert result['metadata']['file_extension'] == ".txt"
        assert result['stored_in_db'] is True
        
        # Verify service calls
        mock_qdrant_service.store_document.assert_called_once()
        mock_azure_service.analyze_document_structure.assert_called_once()
    
    def test_extract_headings_markdown(self, document_processor):
        """Test heading extraction from markdown content"""
        content = """# Main Title
        
## Section 1
Content for section 1.

### Subsection 1.1
Subsection content.

## Section 2
Content for section 2.
"""
        
        # Test
        headings = document_processor._extract_headings(content)
        
        # Assertions
        assert len(headings) == 4
        assert headings[0]['title'] == "Main Title"
        assert headings[0]['level'] == 1
        assert headings[0]['type'] == "markdown"
        
        assert headings[1]['title'] == "Section 1"
        assert headings[1]['level'] == 2
        
        assert headings[2]['title'] == "Subsection 1.1"
        assert headings[2]['level'] == 3
    
    def test_extract_headings_numbered(self, document_processor):
        """Test heading extraction from numbered content"""
        content = """1. First Section
Content here.

1.1 Subsection
More content.

1.2 Another Subsection
Even more content.

2. Second Section
Final content.
"""
        
        # Test
        headings = document_processor._extract_headings(content)
        
        # Assertions
        numbered_headings = [h for h in headings if h['type'] == 'numbered']
        assert len(numbered_headings) >= 3
        
        # Check first numbered heading
        first_numbered = numbered_headings[0]
        assert "First Section" in first_numbered['title']
        assert first_numbered['level'] == 1
    
    def test_extract_headings_caps(self, document_processor):
        """Test heading extraction from all-caps content"""
        content = """EXECUTIVE SUMMARY
This is the executive summary.

TECHNICAL REQUIREMENTS
These are the technical requirements.

EVALUATION CRITERIA
These are the evaluation criteria.
"""
        
        # Test
        headings = document_processor._extract_headings(content)
        
        # Assertions
        caps_headings = [h for h in headings if h['type'] == 'caps']
        assert len(caps_headings) == 3
        
        assert caps_headings[0]['title'] == "EXECUTIVE SUMMARY"
        assert caps_headings[1]['title'] == "TECHNICAL REQUIREMENTS"
        assert caps_headings[2]['title'] == "EVALUATION CRITERIA"
    
    def test_generate_sections(self, document_processor):
        """Test section generation from headings"""
        content = """# Main Title
Introduction content.

## Section 1
Section 1 content here.
More content for section 1.

## Section 2
Section 2 content here.
"""
        
        # Extract headings first
        headings = document_processor._extract_headings(content)
        
        # Test
        sections = document_processor._generate_sections(content, headings)
        
        # Assertions
        assert len(sections) == len(headings)
        
        # Check first section
        first_section = sections[0]
        assert first_section['heading']['title'] == "Main Title"
        assert "Introduction content" in first_section['content']
        assert first_section['word_count'] > 0
    
    def test_generate_table_of_contents(self, document_processor):
        """Test table of contents generation"""
        headings = [
            {'level': 1, 'title': 'Main Title', 'line_number': 1},
            {'level': 2, 'title': 'Section 1', 'line_number': 5},
            {'level': 3, 'title': 'Subsection 1.1', 'line_number': 10},
            {'level': 2, 'title': 'Section 2', 'line_number': 15}
        ]
        
        # Test
        toc = document_processor._generate_table_of_contents(headings)
        
        # Assertions
        assert len(toc) == 4
        
        # Check indentation
        assert toc[0]['formatted'] == "Main Title"  # Level 1, no indent
        assert toc[1]['formatted'] == "  Section 1"  # Level 2, 2 spaces
        assert toc[2]['formatted'] == "    Subsection 1.1"  # Level 3, 4 spaces
        assert toc[3]['formatted'] == "  Section 2"  # Level 2, 2 spaces
    
    @pytest.mark.asyncio
    async def test_analyze_document_structure(self, document_processor, mock_azure_service):
        """Test document structure analysis"""
        content = """# RFP for Software Development
        
## 1. Project Overview
This project involves developing a web application.

## 2. Technical Requirements
- Python backend
- React frontend
- PostgreSQL database

## 3. Timeline
6 months development timeline.
"""
        
        # Test
        structure = await document_processor._analyze_document_structure(content)
        
        # Assertions
        assert isinstance(structure, DocumentStructure)
        assert len(structure.headings) > 0
        assert len(structure.sections) > 0
        assert len(structure.table_of_contents) > 0
        
        # Verify AI service was called
        mock_azure_service.analyze_document_structure.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_section_prompts(self, document_processor, mock_azure_service, mock_qdrant_service):
        """Test section prompt generation"""
        document_id = "test_doc_1"
        sections = [
            {
                'heading': {'title': 'Project Overview'},
                'content': 'This section describes the project overview.'
            },
            {
                'heading': {'title': 'Technical Requirements'},
                'content': 'This section lists technical requirements.'
            }
        ]
        
        # Test
        prompts = await document_processor.generate_section_prompts(document_id, sections)
        
        # Assertions
        assert isinstance(prompts, dict)
        assert len(prompts) == 2
        assert 'Project Overview' in prompts
        assert 'Technical Requirements' in prompts
        
        # Verify AI service calls
        assert mock_azure_service.generate_contextual_prompt.call_count == 2
    
    @pytest.mark.asyncio
    async def test_extract_pdf_content_error(self, document_processor):
        """Test PDF content extraction error handling"""
        # Create invalid PDF file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            tmp_file.write(b'Invalid PDF content')
            invalid_pdf_path = tmp_file.name
        
        try:
            # Test
            with pytest.raises(Exception):
                await document_processor._extract_pdf_content(invalid_pdf_path)
        finally:
            # Cleanup
            os.unlink(invalid_pdf_path)
    
    @pytest.mark.asyncio
    async def test_extract_text_content(self, document_processor):
        """Test text content extraction"""
        content = "This is test content for extraction."
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # Test
            extracted_content = await document_processor._extract_text_content(tmp_file_path)
            
            # Assertions
            assert extracted_content == content
        finally:
            # Cleanup
            os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_unsupported_file_format(self, document_processor, mock_azure_service, mock_qdrant_service):
        """Test handling of unsupported file formats"""
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as tmp_file:
            tmp_file_path = tmp_file.name
        
        try:
            # Test
            with pytest.raises(ValueError) as exc_info:
                await document_processor.process_uploaded_file(
                    file_path=tmp_file_path,
                    file_name="test.xyz",
                    document_type="RFP"
                )
            
            assert "Unsupported file format" in str(exc_info.value)
        finally:
            # Cleanup
            os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_process_uploaded_file_storage_failure(self, document_processor, sample_text_file, mock_azure_service, mock_qdrant_service):
        """Test handling of storage failure"""
        file_path, _ = sample_text_file
        
        # Mock storage failure
        mock_qdrant_service.store_document.return_value = False
        
        # Test
        result = await document_processor.process_uploaded_file(
            file_path=file_path,
            file_name="test.txt",
            document_type="RFP"
        )
        
        # Assertions
        assert result['stored_in_db'] is False
    
    def test_supported_formats(self, document_processor):
        """Test supported file formats"""
        expected_formats = ['.pdf', '.docx', '.txt', '.md']
        assert document_processor.supported_formats == expected_formats


if __name__ == "__main__":
    pytest.main([__file__])
