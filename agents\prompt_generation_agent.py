"""
Prompt Generation Agent for RFP/RFO document processing
Specialized in generating contextual prompts for document sections
"""
import logging
from typing import Dict, List, Any, Optional
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from services.azure_openai_service import azure_openai_service
from services.qdrant_service import qdrant_service

logger = logging.getLogger(__name__)


class PromptGenerationAgent:
    """Agent specialized in generating contextual prompts for document sections"""
    
    def __init__(self):
        """Initialize the prompt generation agent"""
        self.agent_name = "PromptGenerationAgent"
        self.capabilities = [
            "contextual_prompt_generation",
            "section_specific_prompts",
            "requirement_based_prompts",
            "template_customization",
            "prompt_optimization"
        ]
        
        self.system_message = """You are a specialized Prompt Generation Agent for RFP/RFO processing.
Your primary responsibilities are:

1. CONTEXTUAL PROMPT CREATION:
   - Generate specific, actionable prompts for each document section
   - Tailor prompts based on section content and context
   - Ensure prompts guide comprehensive content creation
   - Adapt prompts to document type (RFP, RFO, SOW, etc.)

2. REQUIREMENT-DRIVEN PROMPTS:
   - Create prompts that address specific requirements
   - Include compliance and regulatory considerations
   - Incorporate technical specifications and constraints
   - Address evaluation criteria and success metrics

3. PROMPT OPTIMIZATION:
   - Ensure prompts are clear, specific, and actionable
   - Include relevant examples and guidance
   - Optimize for completeness and accuracy
   - Provide structured response formats

4. TEMPLATE CUSTOMIZATION:
   - Adapt prompts to organizational standards
   - Include industry-specific considerations
   - Customize for different stakeholder audiences
   - Maintain consistency across document sections

Generate prompts that are detailed, specific, and guide users to create comprehensive, compliant responses."""
        
        logger.info(f"Initialized {self.agent_name}")
    
    async def generate_section_prompts(
        self,
        document_id: str,
        sections: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Generate contextual prompts for document sections
        
        Args:
            document_id: Document identifier
            sections: Specific sections to generate prompts for (optional)
            
        Returns:
            Generated prompts for each section
        """
        try:
            logger.info(f"{self.agent_name}: Generating prompts for document {document_id}")
            
            # Get document structure and content
            doc_structure = qdrant_service.get_document_structure(document_id)
            
            if not doc_structure:
                return {'error': 'Document not found', 'agent': self.agent_name}
            
            # Get document metadata
            metadata = doc_structure.get('metadata', {})
            document_type = metadata.get('document_type', 'RFP')
            
            # Generate prompts for each section
            section_prompts = {}
            
            # If specific sections requested, filter to those
            chunks = doc_structure.get('chunks', [])
            if sections:
                chunks = [chunk for chunk in chunks if any(section.lower() in chunk.get('content', '').lower() for section in sections)]
            
            for i, chunk in enumerate(chunks):
                section_content = chunk.get('content', '')
                
                # Extract section heading if available
                section_heading = self._extract_section_heading(section_content)
                
                # Generate contextual prompt
                prompt = await self._generate_contextual_prompt(
                    heading=section_heading,
                    content=section_content,
                    document_type=document_type,
                    context=metadata
                )
                
                section_key = section_heading or f"Section_{i+1}"
                section_prompts[section_key] = {
                    'prompt': prompt,
                    'section_content': section_content[:500] + "..." if len(section_content) > 500 else section_content,
                    'chunk_id': chunk.get('chunk_id', i),
                    'generated_at': metadata.get('upload_date')
                }
            
            return {
                'agent': self.agent_name,
                'document_id': document_id,
                'document_type': document_type,
                'section_prompts': section_prompts,
                'total_prompts': len(section_prompts),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"{self.agent_name}: Error generating prompts for {document_id}: {str(e)}")
            return {
                'agent': self.agent_name,
                'document_id': document_id,
                'status': 'error',
                'error': str(e)
            }
    
    async def _generate_contextual_prompt(
        self,
        heading: str,
        content: str,
        document_type: str,
        context: Dict[str, Any]
    ) -> str:
        """
        Generate a contextual prompt for a specific section
        
        Args:
            heading: Section heading
            content: Section content
            document_type: Type of document
            context: Additional context information
            
        Returns:
            Generated prompt
        """
        try:
            # Limit content for API call
            content_sample = content[:2000]
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Generate a detailed, actionable prompt for this {document_type} section:

SECTION HEADING: {heading}

SECTION CONTENT:
{content_sample}

DOCUMENT CONTEXT:
- Document Type: {document_type}
- Title: {context.get('title', 'Unknown')}

Please create a prompt that:
1. Guides comprehensive content creation for this section
2. Addresses specific requirements mentioned in the content
3. Includes relevant compliance and regulatory considerations
4. Provides clear structure and formatting guidance
5. Includes examples or templates where helpful
6. Ensures completeness and accuracy

The prompt should be detailed enough that someone unfamiliar with the original document could create appropriate content for this section.

Format the prompt as a clear, actionable instruction set.""")
            ]
            
            prompt = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.7
            )
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error generating contextual prompt: {str(e)}")
            return f"Create comprehensive content for the '{heading}' section of this {document_type}. Include all relevant details, requirements, and specifications mentioned in the original document."
    
    def _extract_section_heading(self, content: str) -> str:
        """Extract section heading from content"""
        try:
            lines = content.split('\n')
            
            for line in lines[:5]:  # Check first 5 lines
                line = line.strip()
                
                # Markdown heading
                if line.startswith('#'):
                    return line.lstrip('#').strip()
                
                # All caps heading
                elif line.isupper() and len(line) > 3 and len(line) < 100:
                    return line
                
                # Numbered heading
                elif line and len(line) < 100 and any(char.isdigit() for char in line[:10]):
                    import re
                    if re.match(r'^\d+\.(\d+\.)*\s+', line):
                        return re.sub(r'^\d+\.(\d+\.)*\s+', '', line)
            
            # If no clear heading found, use first meaningful line
            for line in lines[:3]:
                line = line.strip()
                if line and len(line) > 10 and len(line) < 100:
                    return line
            
            return "Untitled Section"
            
        except Exception as e:
            logger.error(f"Error extracting section heading: {str(e)}")
            return "Untitled Section"
    
    async def generate_requirement_prompts(
        self,
        document_id: str,
        requirements: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Generate prompts specifically for addressing requirements
        
        Args:
            document_id: Document identifier
            requirements: List of extracted requirements
            
        Returns:
            Requirement-specific prompts
        """
        try:
            logger.info(f"{self.agent_name}: Generating requirement prompts for {document_id}")
            
            requirement_prompts = {}
            
            for i, requirement in enumerate(requirements):
                req_context = requirement.get('context', '')
                req_pattern = requirement.get('pattern', '')
                
                messages = [
                    SystemMessage(content=self.system_message),
                    HumanMessage(content=f"""Generate a specific prompt to address this requirement:

REQUIREMENT CONTEXT:
{req_context}

REQUIREMENT PATTERN: {req_pattern}

Create a prompt that:
1. Clearly addresses the specific requirement
2. Provides guidance on compliance demonstration
3. Includes necessary documentation or evidence
4. Suggests response structure and format
5. Highlights critical success factors

The prompt should help someone create a comprehensive response that fully satisfies this requirement.""")
                ]
                
                prompt = await azure_openai_service.chat_completion(
                    messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                    temperature=0.6
                )
                
                requirement_prompts[f"Requirement_{i+1}"] = {
                    'prompt': prompt,
                    'requirement_context': req_context,
                    'requirement_pattern': req_pattern
                }
            
            return {
                'agent': self.agent_name,
                'document_id': document_id,
                'requirement_prompts': requirement_prompts,
                'total_requirements': len(requirements),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error generating requirement prompts: {str(e)}")
            return {
                'agent': self.agent_name,
                'status': 'error',
                'error': str(e)
            }
    
    async def optimize_prompt(
        self,
        original_prompt: str,
        section_context: str,
        optimization_goals: List[str] = None
    ) -> Dict[str, Any]:
        """
        Optimize an existing prompt for better results
        
        Args:
            original_prompt: The prompt to optimize
            section_context: Context of the section
            optimization_goals: Specific optimization objectives
            
        Returns:
            Optimized prompt and improvement suggestions
        """
        try:
            if optimization_goals is None:
                optimization_goals = ["clarity", "specificity", "completeness", "actionability"]
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Optimize this prompt for better results:

ORIGINAL PROMPT:
{original_prompt}

SECTION CONTEXT:
{section_context[:1000]}

OPTIMIZATION GOALS:
{', '.join(optimization_goals)}

Please provide:
1. OPTIMIZED PROMPT (improved version)
2. KEY IMPROVEMENTS MADE
3. RATIONALE FOR CHANGES
4. ADDITIONAL SUGGESTIONS

Focus on making the prompt more specific, actionable, and likely to produce comprehensive responses.""")
            ]
            
            optimization_result = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.5
            )
            
            return {
                'agent': self.agent_name,
                'original_prompt': original_prompt,
                'optimization_result': optimization_result,
                'optimization_goals': optimization_goals,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error optimizing prompt: {str(e)}")
            return {
                'agent': self.agent_name,
                'status': 'error',
                'error': str(e)
            }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and capabilities"""
        return {
            'agent_name': self.agent_name,
            'status': 'active',
            'capabilities': self.capabilities,
            'version': '1.0.0'
        }
