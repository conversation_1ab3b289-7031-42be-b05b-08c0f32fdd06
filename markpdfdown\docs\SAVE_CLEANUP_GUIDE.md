# Save and Cleanup Functionality Guide

## Overview

MarkPDFDown now includes comprehensive save and cleanup options that allow you to:

1. **Save combined markdown to files** instead of just stdout
2. **Automatically clean up all intermediate files** after processing
3. **Generate meaningful filenames** based on input or context
4. **Choose between different cleanup behaviors** based on your needs

## New Command Line Options

### `--output filename.md` or `-o filename.md`
Saves the combined markdown output to the specified file (in addition to stdout).

```bash
# Save to specific file
python main.py --output report.md < document.pdf

# Resume and save to file  
python main.py --resume output/20250627144306/ --output final_report.md
```

### `--save-and-cleanup`
Saves the combined markdown to an auto-generated filename and cleans up all intermediate files.

```bash
# Process and auto-save with cleanup
python main.py --save-and-cleanup < document.pdf
# Creates: markpdfdown_output_20250627_143022.md

# Resume, save, and cleanup
python main.py --resume output/20250627144306/ --save-and-cleanup  
# Creates: resumed_20250627144306.md
```

### `--no-cleanup` (existing, enhanced)
Keeps all intermediate files for debugging or later resume.

```bash
# Keep all files
python main.py --no-cleanup < document.pdf

# Save to file and keep intermediate files
python main.py --output report.md --no-cleanup < document.pdf
```

## Filename Generation

The tool automatically generates meaningful filenames when using `--save-and-cleanup`:

### For New Processing:
- **With recognizable input**: `document_name_converted_20250627_143022.md`
- **From stdin/unknown**: `markpdfdown_output_20250627_143022.md`

### For Resume Operations:
- **From resume directory**: `resumed_20250627144306.md`
- **Custom output**: Uses your specified filename

## Behavior Matrix

| Command | Stdout Output | File Output | Intermediate Files | Final Combined File |
|---------|---------------|-------------|-------------------|-------------------|
| `python main.py < file.pdf` | ✅ | ❌ | ❌ (cleaned) | ❌ |
| `--output file.md` | ✅ | ✅ | ❌ (cleaned) | ✅ (preserved) |
| `--save-and-cleanup` | ❌ | ✅ (auto-named) | ❌ (cleaned) | ✅ (preserved) |
| `--no-cleanup` | ✅ | ❌ | ✅ (kept) | ❌ |
| `--output file.md --no-cleanup` | ✅ | ✅ | ✅ (kept) | ✅ (preserved) |
| `--save-and-cleanup --output file.md` | ❌ | ✅ (custom name) | ❌ (cleaned) | ✅ (preserved) |

## Usage Patterns

### 1. Quick Processing (Default)
```bash
# Traditional usage - output to stdout, cleanup automatically
python main.py < document.pdf > output.md
```

### 2. Development/Debugging
```bash
# Keep all files for inspection
python main.py --no-cleanup < document.pdf > output.md

# Resume from specific directory
python main.py --resume output/20250627144306/
```

### 3. Production Processing
```bash
# Process and save to file with auto-cleanup (recommended for production)
python main.py --save-and-cleanup < important_document.pdf

# Custom filename in production
python main.py --save-and-cleanup --output quarterly_report.md < report.pdf
```

### 4. Large File Processing
```bash
# Start processing large file (keep intermediate files for resume)
python main.py --no-cleanup < large_document.pdf > output.md

# If interrupted, resume and finalize
python main.py --resume output/20250627144306/ --save-and-cleanup
```

### 5. Batch Processing
```bash
# Process multiple files with cleanup
for file in *.pdf; do
    python main.py --save-and-cleanup < "$file"
done
```

## Error Handling

### File Save Errors
If saving to file fails, the tool will:
1. Log the error
2. Fall back to stdout output  
3. Continue with cleanup (if specified)

### Cleanup Errors
If cleanup fails, the tool will:
1. Log a warning
2. Continue execution
3. Exit successfully (cleanup failure is non-fatal)

## Directory Structure Examples

### During Processing (--no-cleanup):
```
output/
└── 20250627144306/
    ├── input.pdf
    ├── page_001.jpg
    ├── page_002.jpg
    ├── page_001.jpg.md
    └── page_002.jpg.md
```

### After --save-and-cleanup:
```
markpdfdown_output_20250627_144306.md  (final combined file - PRESERVED)
# output/ directory and all intermediate files - CLEANED UP
```

### With --output and --no-cleanup:
```
my_document.md                          (your specified output - PRESERVED)
output/
└── 20250627144306/                     (preserved for resume)
    ├── input.pdf
    ├── page_001.jpg
    ├── page_002.jpg
    ├── page_001.jpg.md
    └── page_002.jpg.md
```

### With --save-and-cleanup --output custom.md:
```
custom.md                               (your specified output - PRESERVED)
# output/ directory and all intermediate files - CLEANED UP
```

## Best Practices

### For Development:
- Use `--no-cleanup` to inspect intermediate files
- Use `--output` to save results while keeping files for resume

### For Production:
- Use `--save-and-cleanup` for clean, automated processing
- Specify `--output` with meaningful names for important documents

### For Large Files:
- Start with `--no-cleanup` to enable resume if needed
- Finish with `--save-and-cleanup` when processing is complete

### For Error Recovery:
- Always use `--list-dirs` to check available resume directories
- Use `--save-and-cleanup` when resuming to finalize processing

## Migration from Previous Versions

The new options are fully backward compatible:

```bash
# Old way (still works)
python main.py < file.pdf > output.md

# New way with same result + automatic cleanup
python main.py --save-and-cleanup --output output.md < file.pdf

# New way with better defaults
python main.py --save-and-cleanup < file.pdf
```

This functionality makes MarkPDFDown more suitable for production use, automated workflows, and better file management.
