"""
Document Processing Service for RFP/RFO documents
Handles PDF extraction, structure analysis, and content processing
"""
import os
import re
import logging
import tempfile
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import subprocess
import sys

# Add markpdfdown to path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'markpdfdown'))

from markpdfdown.core.PDFWorker import PDFWorker
from services.azure_openai_service import azure_openai_service
from services.qdrant_service import qdrant_service

logger = logging.getLogger(__name__)


class DocumentStructure:
    """Class to represent document structure"""
    
    def __init__(self):
        self.headings: List[Dict[str, Any]] = []
        self.sections: List[Dict[str, Any]] = []
        self.table_of_contents: List[Dict[str, Any]] = []
        self.document_type: str = "unknown"
        self.metadata: Dict[str, Any] = {}


class DocumentProcessor:
    """Service for processing RFP/RFO documents"""
    
    def __init__(self):
        """Initialize document processor"""
        self.supported_formats = ['.pdf', '.docx', '.txt', '.md']
        logger.info("Document processor initialized")
    
    async def process_uploaded_file(
        self,
        file_path: str,
        file_name: str,
        document_type: str = "RFP"
    ) -> Dict[str, Any]:
        """
        Process an uploaded document file
        
        Args:
            file_path: Path to the uploaded file
            file_name: Original filename
            document_type: Type of document (RFP, RFO, etc.)
            
        Returns:
            Processing results including structure and content
        """
        try:
            logger.info(f"Processing uploaded file: {file_name}")
            
            # Extract file extension
            file_ext = os.path.splitext(file_name)[1].lower()
            
            if file_ext not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {file_ext}")
            
            # Extract content based on file type
            if file_ext == '.pdf':
                content = await self._extract_pdf_content(file_path)
            elif file_ext == '.docx':
                content = await self._extract_docx_content(file_path)
            elif file_ext in ['.txt', '.md']:
                content = await self._extract_text_content(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_ext}")
            
            # Analyze document structure
            structure = await self._analyze_document_structure(content)
            
            # Generate document ID
            document_id = f"{document_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(file_name) % 10000}"
            
            # Prepare metadata
            metadata = {
                'title': file_name,
                'document_type': document_type,
                'upload_date': datetime.now().isoformat(),
                'file_extension': file_ext,
                'content_length': len(content),
                'structure': structure.__dict__
            }
            
            # Store in vector database
            success = await qdrant_service.store_document(
                document_id=document_id,
                content=content,
                metadata=metadata
            )
            
            if not success:
                logger.warning(f"Failed to store document {document_id} in vector database")
            
            return {
                'document_id': document_id,
                'content': content,
                'structure': structure,
                'metadata': metadata,
                'stored_in_db': success
            }
            
        except Exception as e:
            logger.error(f"Error processing uploaded file {file_name}: {str(e)}")
            raise
    
    async def _extract_pdf_content(self, file_path: str) -> str:
        """Extract content from PDF using MarkdownPDF"""
        try:
            # Use PDFWorker from markpdfdown
            pdf_worker = PDFWorker(file_path)
            
            # Extract text content
            # Note: This is a simplified extraction. The actual MarkdownPDF
            # implementation may have more sophisticated methods
            content = ""
            
            # Try to extract text from PDF
            for page_num in range(pdf_worker.get_total_pages()):
                try:
                    page = pdf_worker.reader.pages[page_num]
                    page_text = page.extract_text()
                    content += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
                except Exception as e:
                    logger.warning(f"Error extracting page {page_num + 1}: {str(e)}")
                    continue
            
            if not content.strip():
                raise ValueError("No text content could be extracted from PDF")
            
            return content
            
        except Exception as e:
            logger.error(f"Error extracting PDF content: {str(e)}")
            raise
    
    async def _extract_docx_content(self, file_path: str) -> str:
        """Extract content from DOCX file"""
        try:
            from docx import Document
            
            doc = Document(file_path)
            content = ""
            
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            
            return content
            
        except Exception as e:
            logger.error(f"Error extracting DOCX content: {str(e)}")
            raise
    
    async def _extract_text_content(self, file_path: str) -> str:
        """Extract content from text/markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
                
        except Exception as e:
            logger.error(f"Error extracting text content: {str(e)}")
            raise
    
    async def _analyze_document_structure(self, content: str) -> DocumentStructure:
        """
        Analyze document structure and hierarchy
        
        Args:
            content: Document content to analyze
            
        Returns:
            DocumentStructure object with parsed structure
        """
        try:
            structure = DocumentStructure()
            
            # Parse headings using regex patterns
            structure.headings = self._extract_headings(content)
            
            # Generate sections based on headings
            structure.sections = self._generate_sections(content, structure.headings)
            
            # Create table of contents
            structure.table_of_contents = self._generate_table_of_contents(structure.headings)
            
            # Use AI to enhance structure analysis
            ai_analysis = await azure_openai_service.analyze_document_structure(content)
            
            # Merge AI analysis with regex-based analysis
            structure.document_type = ai_analysis.get('document_type', 'unknown')
            structure.metadata = ai_analysis
            
            return structure
            
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            # Return basic structure on error
            structure = DocumentStructure()
            structure.document_type = "unknown"
            return structure
    
    def _extract_headings(self, content: str) -> List[Dict[str, Any]]:
        """Extract headings from document content"""
        headings = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Markdown-style headings
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('#').strip()
                headings.append({
                    'level': level,
                    'title': title,
                    'line_number': i + 1,
                    'type': 'markdown'
                })
            
            # All caps headings (common in legal documents)
            elif line.isupper() and len(line) > 3 and not line.startswith('PAGE'):
                headings.append({
                    'level': 1,
                    'title': line,
                    'line_number': i + 1,
                    'type': 'caps'
                })
            
            # Numbered headings (1., 1.1, etc.)
            elif re.match(r'^\d+\.(\d+\.)*\s+', line):
                level = line.count('.') 
                title = re.sub(r'^\d+\.(\d+\.)*\s+', '', line)
                headings.append({
                    'level': level,
                    'title': title,
                    'line_number': i + 1,
                    'type': 'numbered'
                })
        
        return headings
    
    def _generate_sections(self, content: str, headings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate sections based on headings"""
        sections = []
        lines = content.split('\n')
        
        for i, heading in enumerate(headings):
            start_line = heading['line_number']
            
            # Find end line (next heading of same or higher level)
            end_line = len(lines)
            for j in range(i + 1, len(headings)):
                if headings[j]['level'] <= heading['level']:
                    end_line = headings[j]['line_number'] - 1
                    break
            
            # Extract section content
            section_lines = lines[start_line:end_line]
            section_content = '\n'.join(section_lines).strip()
            
            sections.append({
                'heading': heading,
                'content': section_content,
                'start_line': start_line,
                'end_line': end_line,
                'word_count': len(section_content.split())
            })
        
        return sections
    
    def _generate_table_of_contents(self, headings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate table of contents from headings"""
        toc = []
        
        for heading in headings:
            indent = "  " * (heading['level'] - 1)
            toc.append({
                'level': heading['level'],
                'title': heading['title'],
                'line_number': heading['line_number'],
                'formatted': f"{indent}{heading['title']}"
            })
        
        return toc
    
    async def generate_section_prompts(
        self,
        document_id: str,
        sections: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        Generate contextual prompts for each section
        
        Args:
            document_id: Document identifier
            sections: List of document sections
            
        Returns:
            Dictionary mapping section titles to generated prompts
        """
        try:
            prompts = {}
            
            # Get document context from vector database
            doc_structure = qdrant_service.get_document_structure(document_id)
            document_type = doc_structure.get('metadata', {}).get('document_type', 'RFP')
            
            for section in sections:
                heading = section['heading']['title']
                content = section['content']
                
                # Generate contextual prompt
                prompt = await azure_openai_service.generate_contextual_prompt(
                    heading=heading,
                    context=content,
                    document_type=document_type
                )
                
                prompts[heading] = prompt
            
            return prompts
            
        except Exception as e:
            logger.error(f"Error generating section prompts: {str(e)}")
            return {}


# Global instance
document_processor = DocumentProcessor()
