# RFP/RFO Document Processing System

A comprehensive AI-powered document processing system for RFP (Request for Proposal) and RFO (Request for Offer) documents, featuring multi-agent orchestration, Azure OpenAI integration, and vector database storage.

## 🌟 Features

### Core Capabilities
- **Multi-Agent Document Processing**: Specialized agents for parsing, content analysis, prompt generation, and quality assurance
- **Azure OpenAI Integration**: Uses azure-text-embedding-3-large for document embeddings and GPT models for analysis
- **Vector Database Storage**: Local Qdrant integration for document indexing and similarity search
- **Document Structure Analysis**: Automatic extraction of headings, sections, and hierarchical structure
- **Interactive Prompt Generation**: AI-generated contextual prompts with editing capabilities
- **Comprehensive Quality Assurance**: Automated validation and quality scoring

### Document Processing Pipeline
1. **Document Upload**: Support for PDF, DOCX, TXT, and Markdown files
2. **Structure Extraction**: Intelligent parsing of document hierarchy and sections
3. **Content Analysis**: Requirements extraction, compliance checking, and content categorization
4. **Prompt Generation**: Contextual prompts for each document section
5. **Quality Validation**: Comprehensive quality assessment and recommendations

### Multi-Agent Architecture
- **DocumentParsingAgent**: Specialized in document structure extraction and parsing
- **ContentAnalysisAgent**: Focuses on content analysis, requirements extraction, and compliance checking
- **PromptGenerationAgent**: Generates contextual prompts for document sections
- **QualityAssuranceAgent**: Validates processing quality and provides recommendations

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Azure OpenAI account with API access
- Docker (for Qdrant vector database)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd AI360-RFP_RFO
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Start Qdrant vector database**
```bash
docker-compose -f docker-compose-qdrant.yml up -d
```

4. **Configure environment variables**
Create or update `markpdfdown/.env`:
```env
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key_here
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name
AZURE_OPENAI_API_VERSION=2024-02-01
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-large
```

5. **Run the application**
```bash
# Start the enhanced document processing UI
streamlit run ui/document_processing_app.py

# Or run the original procurement UI
streamlit run ui/app.py
```

## 📖 Usage

### Document Processing Workflow

1. **Upload Document**: Navigate to the "Document Upload" page and upload your RFP/RFO document
2. **Select Processing Options**: Choose analysis types and enable prompt generation
3. **Process Document**: Click "Process Document" to start the multi-agent workflow
4. **Review Results**: Examine the processing results across multiple tabs:
   - Document Structure: View extracted headings and sections
   - Content Analysis: Review requirements and compliance elements
   - Generated Prompts: Edit and customize AI-generated prompts
   - Quality Report: Check processing quality and recommendations

### Interactive Features

- **Prompt Editor**: Customize generated prompts for specific needs
- **Auto-Generate**: Regenerate prompts with improved context
- **Export/Import**: Save and load prompt configurations
- **Document Library**: Manage processed documents
- **System Status**: Monitor agent and database status

## 🏗️ Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Streamlit UI Layer                       │
├─────────────────────────────────────────────────────────────┤
│                Multi-Agent Workflow (LangGraph)            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌────────┐ │
│  │ Document    │ │ Content     │ │ Prompt      │ │Quality │ │
│  │ Parsing     │ │ Analysis    │ │ Generation  │ │Assur.  │ │
│  │ Agent       │ │ Agent       │ │ Agent       │ │Agent   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Azure       │ │ Qdrant      │ │ Document                │ │
│  │ OpenAI      │ │ Vector DB   │ │ Processor               │ │
│  │ Service     │ │ Service     │ │ Service                 │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                External Dependencies                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Azure       │ │ Qdrant      │ │ MarkdownPDF             │ │
│  │ OpenAI API  │ │ Database    │ │ Processor               │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow

1. **Document Upload** → Document Processor → Structure Analysis
2. **Content Extraction** → Azure OpenAI → Embeddings Generation
3. **Vector Storage** → Qdrant Database → Similarity Search
4. **Multi-Agent Processing** → LangGraph Workflow → Quality Validation
5. **Results Presentation** → Streamlit UI → Interactive Editing

## 🧪 Testing

### Run All Tests
```bash
python tests/run_tests.py --verbose --coverage
```

### Run Specific Test Suites
```bash
# Unit tests only
python tests/run_tests.py --unit-only

# Integration tests only
python tests/run_tests.py --integration-only

# System health check
python tests/run_tests.py --health-check
```

### Test Coverage
- **Unit Tests**: Individual component testing with mocks
- **Integration Tests**: End-to-end workflow testing
- **Performance Tests**: Large document processing validation
- **Error Handling**: Resilience and recovery testing

## 📁 Project Structure

```
AI360-RFP_RFO/
├── agents/                     # Multi-agent system
│   ├── document_parsing_agent.py
│   ├── content_analysis_agent.py
│   ├── prompt_generation_agent.py
│   └── quality_assurance_agent.py
├── services/                   # Core services
│   ├── azure_openai_service.py
│   ├── qdrant_service.py
│   └── document_processor.py
├── workflows/                  # LangGraph workflows
│   ├── procurement_workflow.py
│   └── document_processing_workflow.py
├── ui/                        # User interfaces
│   ├── app.py                 # Original procurement UI
│   └── document_processing_app.py  # Enhanced document processing UI
├── tests/                     # Comprehensive test suite
│   ├── test_azure_openai_service.py
│   ├── test_qdrant_service.py
│   ├── test_document_processor.py
│   ├── test_document_processing_workflow.py
│   ├── test_integration.py
│   └── run_tests.py
├── config/                    # Configuration
│   └── settings.py
├── models/                    # Data models
│   ├── request.py
│   └── rfp.py
├── markpdfdown/              # PDF processing integration
├── RFP_sample/               # Sample documents
└── docker-compose-qdrant.yml # Vector database setup
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AZURE_OPENAI_ENDPOINT` | Azure OpenAI service endpoint | Yes |
| `AZURE_OPENAI_API_KEY` | Azure OpenAI API key | Yes |
| `AZURE_OPENAI_DEPLOYMENT_NAME` | GPT model deployment name | Yes |
| `AZURE_OPENAI_EMBEDDING_DEPLOYMENT` | Embedding model deployment | No |
| `QDRANT_HOST` | Qdrant database host | No (default: localhost) |
| `QDRANT_PORT` | Qdrant database port | No (default: 6333) |
| `QDRANT_COLLECTION_NAME` | Vector collection name | No (default: rfp_documents) |

### Azure OpenAI Setup

1. Create an Azure OpenAI resource in Azure Portal
2. Deploy the following models:
   - GPT-4 or GPT-3.5-turbo for text generation
   - text-embedding-3-large for embeddings
3. Note the endpoint URL and API key
4. Update the `.env` file with your credentials

### Qdrant Database Setup

The system uses Qdrant as a local vector database:

```bash
# Start Qdrant with Docker
docker-compose -f docker-compose-qdrant.yml up -d

# Verify Qdrant is running
curl http://localhost:6333/health
```

## 🔧 Advanced Configuration

### Custom Agent Configuration

Agents can be customized by modifying their system messages and capabilities:

```python
# Example: Customize the Content Analysis Agent
from agents.content_analysis_agent import ContentAnalysisAgent

agent = ContentAnalysisAgent()
agent.capabilities.append("custom_analysis_type")
```

### Workflow Customization

The LangGraph workflow can be extended with additional processing steps:

```python
# Add custom processing node
graph.add_node("custom_processing", custom_processing_function)
graph.add_edge("analyze_content", "custom_processing")
```

## 📊 Monitoring and Logging

### System Status

Monitor system health through the UI:
- Navigate to "System Status" page
- Check agent status and capabilities
- Monitor database connection and metrics
- View processing statistics

### Logging Configuration

Logs are configured in each module:
- Service-level logging for debugging
- Agent-level logging for workflow tracking
- Error logging for troubleshooting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit a pull request

### Development Guidelines

- Follow PEP 8 style guidelines
- Add type hints to all functions
- Include docstrings for all classes and methods
- Maintain test coverage above 80%
- Update README for new features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Run the health check: `python tests/run_tests.py --health-check`
- Review logs for error details
- Submit issues with detailed reproduction steps

## 🔮 Future Enhancements

- **Multi-language Support**: Process documents in multiple languages
- **Advanced Analytics**: Enhanced document insights and metrics
- **API Integration**: RESTful API for programmatic access
- **Cloud Deployment**: Azure/AWS deployment configurations
- **Real-time Collaboration**: Multi-user document editing
- **Advanced Security**: Enhanced authentication and authorization
