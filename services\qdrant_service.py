"""
Qdrant Vector Database Service for document storage and retrieval
"""
import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from qdrant_client import QdrantClient
from qdrant_client.models import (
    Distance, VectorParams, PointStruct, Filter, FieldCondition, 
    MatchValue, SearchRequest, CollectionInfo
)
from config.settings import QDRANT_HOST, QDRANT_PORT, QDRANT_COLLECTION_NAME
from services.azure_openai_service import azure_openai_service

logger = logging.getLogger(__name__)


class QdrantService:
    """Service for Qdrant vector database operations"""
    
    def __init__(self):
        """Initialize Qdrant client and collection"""
        try:
            self.client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
            self.collection_name = QDRANT_COLLECTION_NAME
            self.vector_size = 3072  # Size for text-embedding-3-large
            
            # Initialize collection if it doesn't exist
            self._ensure_collection_exists()
            
            logger.info(f"Qdrant service initialized with collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant service: {str(e)}")
            raise
    
    def _ensure_collection_exists(self):
        """Ensure the collection exists, create if not"""
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection {self.collection_name} already exists")
                
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {str(e)}")
            raise
    
    async def store_document(
        self,
        document_id: str,
        content: str,
        metadata: Dict[str, Any],
        chunks: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        Store a document with its embeddings in Qdrant
        
        Args:
            document_id: Unique identifier for the document
            content: Full document content
            metadata: Document metadata (title, type, upload_date, etc.)
            chunks: Optional pre-chunked content with metadata
            
        Returns:
            Success status
        """
        try:
            points = []
            
            # If chunks are provided, use them; otherwise create chunks
            if chunks:
                document_chunks = chunks
            else:
                document_chunks = self._chunk_document(content)
            
            # Generate embeddings for all chunks
            chunk_texts = [chunk['content'] for chunk in document_chunks]
            embeddings = await azure_openai_service.get_embeddings(chunk_texts)
            
            # Create points for each chunk
            for i, (chunk, embedding) in enumerate(zip(document_chunks, embeddings)):
                point_id = f"{document_id}_{i}"
                
                # Combine document metadata with chunk metadata
                point_payload = {
                    **metadata,
                    "document_id": document_id,
                    "chunk_id": i,
                    "content": chunk['content'],
                    "chunk_metadata": chunk.get('metadata', {}),
                    "stored_at": datetime.now().isoformat()
                }
                
                points.append(PointStruct(
                    id=point_id,
                    vector=embedding,
                    payload=point_payload
                ))
            
            # Store all points
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Stored document {document_id} with {len(points)} chunks")
            return True
            
        except Exception as e:
            logger.error(f"Error storing document {document_id}: {str(e)}")
            return False
    
    def _chunk_document(self, content: str, chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """
        Chunk document content into smaller pieces
        
        Args:
            content: Document content to chunk
            chunk_size: Maximum size of each chunk
            overlap: Overlap between chunks
            
        Returns:
            List of chunk dictionaries
        """
        chunks = []
        start = 0
        
        while start < len(content):
            end = start + chunk_size
            chunk_content = content[start:end]
            
            # Try to break at sentence boundaries
            if end < len(content):
                last_period = chunk_content.rfind('.')
                last_newline = chunk_content.rfind('\n')
                break_point = max(last_period, last_newline)
                
                if break_point > start + chunk_size // 2:
                    chunk_content = content[start:start + break_point + 1]
                    end = start + break_point + 1
            
            chunks.append({
                'content': chunk_content.strip(),
                'metadata': {
                    'start_pos': start,
                    'end_pos': end,
                    'chunk_length': len(chunk_content)
                }
            })
            
            start = end - overlap
        
        return chunks
    
    async def search_similar_content(
        self,
        query: str,
        limit: int = 10,
        score_threshold: float = 0.7,
        filter_conditions: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar content using vector similarity
        
        Args:
            query: Search query text
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            filter_conditions: Optional filters for metadata
            
        Returns:
            List of similar content with scores
        """
        try:
            # Generate embedding for query
            query_embedding = await azure_openai_service.get_single_embedding(query)
            
            # Build filter if provided
            search_filter = None
            if filter_conditions:
                conditions = []
                for key, value in filter_conditions.items():
                    conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))
                search_filter = Filter(must=conditions)
            
            # Perform search
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                score_threshold=score_threshold,
                query_filter=search_filter,
                with_payload=True
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    'id': result.id,
                    'score': result.score,
                    'content': result.payload.get('content', ''),
                    'metadata': {
                        'document_id': result.payload.get('document_id'),
                        'chunk_id': result.payload.get('chunk_id'),
                        'title': result.payload.get('title'),
                        'document_type': result.payload.get('document_type'),
                        'chunk_metadata': result.payload.get('chunk_metadata', {})
                    }
                })
            
            logger.info(f"Found {len(results)} similar content items for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching similar content: {str(e)}")
            return []
    
    def get_document_structure(self, document_id: str) -> Dict[str, Any]:
        """
        Retrieve document structure and metadata
        
        Args:
            document_id: Document identifier
            
        Returns:
            Document structure information
        """
        try:
            # Search for all chunks of the document
            search_filter = Filter(
                must=[FieldCondition(key="document_id", match=MatchValue(value=document_id))]
            )
            
            results = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=search_filter,
                with_payload=True,
                limit=1000  # Adjust based on expected document size
            )
            
            if not results[0]:  # No points found
                return {}
            
            # Extract structure information
            chunks = []
            document_metadata = {}
            
            for point in results[0]:
                chunks.append({
                    'chunk_id': point.payload.get('chunk_id'),
                    'content': point.payload.get('content', ''),
                    'metadata': point.payload.get('chunk_metadata', {})
                })
                
                # Get document metadata from first chunk
                if not document_metadata:
                    document_metadata = {
                        'title': point.payload.get('title'),
                        'document_type': point.payload.get('document_type'),
                        'upload_date': point.payload.get('upload_date'),
                        'stored_at': point.payload.get('stored_at')
                    }
            
            # Sort chunks by chunk_id
            chunks.sort(key=lambda x: x['chunk_id'])
            
            return {
                'document_id': document_id,
                'metadata': document_metadata,
                'chunks': chunks,
                'total_chunks': len(chunks)
            }
            
        except Exception as e:
            logger.error(f"Error retrieving document structure: {str(e)}")
            return {}
    
    def delete_document(self, document_id: str) -> bool:
        """
        Delete a document and all its chunks
        
        Args:
            document_id: Document identifier
            
        Returns:
            Success status
        """
        try:
            # Delete all points with the document_id
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=Filter(
                    must=[FieldCondition(key="document_id", match=MatchValue(value=document_id))]
                )
            )
            
            logger.info(f"Deleted document {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {str(e)}")
            return False
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection"""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                'name': info.config.params.vectors.size,
                'vectors_count': info.vectors_count,
                'points_count': info.points_count,
                'status': info.status
            }
        except Exception as e:
            logger.error(f"Error getting collection info: {str(e)}")
            return {}


# Global instance
qdrant_service = QdrantService()
