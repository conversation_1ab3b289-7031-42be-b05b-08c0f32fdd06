"""
Integration Tests for RFP/RFO Document Processing System
"""
import pytest
import tempfile
import os
import asyncio
from unittest.mock import patch, <PERSON><PERSON>
import sys

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from workflows.document_processing_workflow import document_processing_workflow
from services.azure_openai_service import azure_openai_service
from services.qdrant_service import qdrant_service
from services.document_processor import document_processor


class TestSystemIntegration:
    """Integration tests for the complete system"""
    
    @pytest.fixture
    def sample_rfp_document(self):
        """Create a comprehensive sample RFP document"""
        content = """# REQUEST FOR PROPOSAL
Software Development Services

## 1. EXECUTIVE SUMMARY
This RFP is for the development of a comprehensive web-based application.

## 2. PROJECT OVERVIEW
### 2.1 Background
Our organization requires a modern web application to streamline operations.

### 2.2 Objectives
- Improve operational efficiency
- Enhance user experience
- Reduce manual processes

## 3. SCOPE OF WORK
### 3.1 Technical Requirements
The solution must include:
- Python/Django backend
- React frontend
- PostgreSQL database
- RESTful API architecture

### 3.2 Functional Requirements
- User authentication and authorization
- Data management capabilities
- Reporting and analytics
- Mobile-responsive design

## 4. COMPLIANCE REQUIREMENTS
### 4.1 Security Standards
- GDPR compliance required
- SOC 2 Type II certification preferred
- Data encryption at rest and in transit

### 4.2 Accessibility
- WCAG 2.1 AA compliance mandatory
- Section 508 compliance required

## 5. PROJECT TIMELINE
### 5.1 Development Phases
- Phase 1: Requirements and Design (2 months)
- Phase 2: Development (4 months)
- Phase 3: Testing and Deployment (2 months)

### 5.2 Key Milestones
- Requirements finalization: Month 2
- MVP delivery: Month 4
- Final delivery: Month 8

## 6. EVALUATION CRITERIA
### 6.1 Technical Capability (40%)
- Demonstrated expertise in required technologies
- Quality of proposed architecture
- Development methodology

### 6.2 Experience (30%)
- Relevant project experience
- Client references
- Team qualifications

### 6.3 Cost (20%)
- Total project cost
- Value for money
- Payment terms

### 6.4 Timeline (10%)
- Realistic project schedule
- Ability to meet deadlines
- Risk mitigation strategies

## 7. SUBMISSION REQUIREMENTS
### 7.1 Proposal Format
Proposals must include:
- Executive summary
- Technical approach
- Project timeline
- Cost breakdown
- Team qualifications

### 7.2 Submission Details
- Deadline: 30 days from RFP issuance
- Format: PDF document
- Maximum length: 50 pages
- Email submission required

## 8. TERMS AND CONDITIONS
### 8.1 Contract Terms
- Fixed-price contract preferred
- Payment milestones based on deliverables
- Intellectual property rights

### 8.2 Legal Requirements
- Valid business license required
- Insurance coverage mandatory
- Background checks for team members
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        yield tmp_file_path, content
        
        # Cleanup
        os.unlink(tmp_file_path)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_end_to_end_document_processing(self, sample_rfp_document):
        """Test complete end-to-end document processing"""
        file_path, expected_content = sample_rfp_document
        
        # Mock external services to avoid actual API calls
        with patch.object(azure_openai_service, 'get_embeddings') as mock_embeddings, \
             patch.object(azure_openai_service, 'chat_completion') as mock_chat, \
             patch.object(azure_openai_service, 'analyze_document_structure') as mock_analyze, \
             patch.object(azure_openai_service, 'generate_contextual_prompt') as mock_prompt, \
             patch.object(qdrant_service, 'client') as mock_qdrant:
            
            # Setup mocks
            mock_embeddings.return_value = [[0.1] * 3072 for _ in range(10)]  # Mock embeddings
            mock_chat.return_value = "Mocked AI response"
            mock_analyze.return_value = {
                'document_type': 'RFP',
                'headings': [],
                'sections': []
            }
            mock_prompt.return_value = "Generated prompt for section"
            
            # Mock Qdrant operations
            mock_qdrant.upsert.return_value = True
            mock_qdrant.search.return_value = []
            mock_qdrant.scroll.return_value = ([], None)
            
            # Execute workflow
            result = await document_processing_workflow.execute(
                file_path=file_path,
                file_name="sample_rfp.txt",
                document_type="RFP"
            )
            
            # Assertions
            assert result['overall_status'] == 'success'
            assert result['document_info']['file_name'] == "sample_rfp.txt"
            assert result['document_info']['document_type'] == "RFP"
            
            # Check parsing results
            parsing_result = result['parsing_result']
            assert parsing_result['parsing_status'] == 'success'
            assert parsing_result['content'] == expected_content
            assert len(parsing_result['structure']['headings']) > 0
            assert len(parsing_result['structure']['sections']) > 0
            
            # Check content analysis
            content_analysis = result['content_analysis']
            assert content_analysis['status'] == 'success'
            assert 'analysis_result' in content_analysis
            
            # Check prompt generation
            prompt_generation = result['prompt_generation']
            assert prompt_generation['status'] == 'success'
            assert prompt_generation['total_prompts'] > 0
            
            # Check quality validation
            quality_validation = result['quality_validation']
            assert quality_validation['status'] == 'success'
            assert 'validation_report' in quality_validation
    
    @pytest.mark.integration
    def test_document_structure_extraction(self, sample_rfp_document):
        """Test document structure extraction accuracy"""
        file_path, content = sample_rfp_document
        
        # Test heading extraction
        headings = document_processor._extract_headings(content)
        
        # Verify key headings are found
        heading_titles = [h['title'] for h in headings]
        
        expected_headings = [
            "REQUEST FOR PROPOSAL",
            "EXECUTIVE SUMMARY",
            "PROJECT OVERVIEW",
            "SCOPE OF WORK",
            "COMPLIANCE REQUIREMENTS",
            "PROJECT TIMELINE",
            "EVALUATION CRITERIA",
            "SUBMISSION REQUIREMENTS",
            "TERMS AND CONDITIONS"
        ]
        
        for expected in expected_headings:
            assert any(expected in title for title in heading_titles), f"Missing heading: {expected}"
        
        # Test section generation
        sections = document_processor._generate_sections(content, headings)
        assert len(sections) == len(headings)
        
        # Verify sections have content
        for section in sections:
            assert section['word_count'] > 0
            assert len(section['content']) > 0
    
    @pytest.mark.integration
    def test_requirement_extraction(self, sample_rfp_document):
        """Test requirement extraction from document"""
        file_path, content = sample_rfp_document
        
        # Test requirement pattern matching
        requirement_patterns = [
            r'must\s+(?:be|have|provide|support|include)',
            r'shall\s+(?:be|have|provide|support|include)',
            r'required\s+to',
            r'mandatory',
            r'compliance'
        ]
        
        import re
        requirements_found = []
        
        for pattern in requirement_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                start = max(0, match.start() - 50)
                end = min(len(content), match.end() + 100)
                context = content[start:end].strip()
                requirements_found.append({
                    'pattern': pattern,
                    'context': context
                })
        
        # Verify requirements are found
        assert len(requirements_found) > 0
        
        # Check for specific requirements in the sample document
        content_lower = content.lower()
        assert 'gdpr compliance required' in content_lower
        assert 'mandatory' in content_lower
        assert 'required' in content_lower
    
    @pytest.mark.integration
    def test_compliance_element_identification(self, sample_rfp_document):
        """Test compliance element identification"""
        file_path, content = sample_rfp_document
        
        compliance_keywords = [
            'gdpr', 'soc 2', 'wcag', 'section 508',
            'compliance', 'certification', 'security'
        ]
        
        compliance_found = []
        content_lower = content.lower()
        
        for keyword in compliance_keywords:
            if keyword in content_lower:
                # Find context around the keyword
                import re
                pattern = rf'\b{re.escape(keyword)}\b'
                matches = re.finditer(pattern, content, re.IGNORECASE)
                
                for match in matches:
                    start = max(0, match.start() - 100)
                    end = min(len(content), match.end() + 100)
                    context = content[start:end].strip()
                    compliance_found.append({
                        'keyword': keyword,
                        'context': context
                    })
        
        # Verify compliance elements are found
        assert len(compliance_found) > 0
        
        # Check for specific compliance requirements
        compliance_keywords_found = [item['keyword'] for item in compliance_found]
        assert 'gdpr' in compliance_keywords_found
        assert 'compliance' in compliance_keywords_found
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_prompt_generation_quality(self, sample_rfp_document):
        """Test quality of generated prompts"""
        file_path, content = sample_rfp_document
        
        # Mock AI service for prompt generation
        with patch.object(azure_openai_service, 'generate_contextual_prompt') as mock_prompt:
            mock_prompt.return_value = """Create a comprehensive response for the 'Technical Requirements' section that includes:

1. Detailed technical specifications for the proposed solution
2. Architecture diagrams and system design
3. Technology stack justification
4. Scalability and performance considerations
5. Security implementation details
6. Integration capabilities
7. Development methodology and best practices

Ensure your response addresses all mandatory requirements mentioned in the RFP and demonstrates deep technical expertise."""
            
            # Extract headings and generate prompts
            headings = document_processor._extract_headings(content)
            sections = document_processor._generate_sections(content, headings)
            
            # Test prompt generation for a few key sections
            test_sections = [s for s in sections if 'technical' in s['heading']['title'].lower()][:3]
            
            for section in test_sections:
                prompt = await azure_openai_service.generate_contextual_prompt(
                    heading=section['heading']['title'],
                    context=section['content'],
                    document_type="RFP"
                )
                
                # Verify prompt quality
                assert len(prompt) > 100  # Substantial content
                assert 'comprehensive' in prompt.lower()  # Actionable language
                assert any(word in prompt.lower() for word in ['create', 'provide', 'include', 'describe'])
    
    @pytest.mark.integration
    def test_table_of_contents_generation(self, sample_rfp_document):
        """Test table of contents generation"""
        file_path, content = sample_rfp_document
        
        # Extract headings and generate TOC
        headings = document_processor._extract_headings(content)
        toc = document_processor._generate_table_of_contents(headings)
        
        # Verify TOC structure
        assert len(toc) > 0
        assert len(toc) == len(headings)
        
        # Check indentation levels
        for item in toc:
            level = item['level']
            formatted = item['formatted']
            expected_indent = "  " * (level - 1)
            assert formatted.startswith(expected_indent)
        
        # Verify key sections are in TOC
        toc_titles = [item['title'] for item in toc]
        assert any('EXECUTIVE SUMMARY' in title for title in toc_titles)
        assert any('SCOPE OF WORK' in title for title in toc_titles)
        assert any('EVALUATION CRITERIA' in title for title in toc_titles)
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_error_recovery_and_resilience(self):
        """Test system error recovery and resilience"""
        # Test with invalid file
        with tempfile.NamedTemporaryFile(suffix='.invalid', delete=False) as tmp_file:
            tmp_file.write(b'Invalid content')
            invalid_file_path = tmp_file.name
        
        try:
            # Should handle invalid file gracefully
            result = await document_processing_workflow.execute(
                file_path=invalid_file_path,
                file_name="invalid.invalid",
                document_type="RFP"
            )
            
            # Should return error status but not crash
            assert result['overall_status'] == 'error'
            assert 'error' in result
            
        finally:
            os.unlink(invalid_file_path)
        
        # Test with empty file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write("")  # Empty content
            empty_file_path = tmp_file.name
        
        try:
            with patch.object(azure_openai_service, 'get_embeddings') as mock_embeddings, \
                 patch.object(qdrant_service, 'client') as mock_qdrant:
                
                mock_embeddings.return_value = [[0.1] * 3072]
                mock_qdrant.upsert.return_value = True
                
                result = await document_processing_workflow.execute(
                    file_path=empty_file_path,
                    file_name="empty.txt",
                    document_type="RFP"
                )
                
                # Should handle empty content gracefully
                assert 'overall_status' in result
                
        finally:
            os.unlink(empty_file_path)
    
    @pytest.mark.integration
    def test_system_configuration_validation(self):
        """Test system configuration and dependencies"""
        # Test workflow status
        status = document_processing_workflow.get_workflow_status()
        
        assert status['workflow_name'] == 'DocumentProcessingWorkflow'
        assert status['status'] == 'active'
        assert len(status['agents']) == 4
        
        # Verify all required agents are present
        agent_names = [agent['agent_name'] for agent in status['agents']]
        required_agents = [
            'DocumentParsingAgent',
            'ContentAnalysisAgent',
            'PromptGenerationAgent',
            'QualityAssuranceAgent'
        ]
        
        for required_agent in required_agents:
            assert required_agent in agent_names
        
        # Test document processor configuration
        assert document_processor.supported_formats == ['.pdf', '.docx', '.txt', '.md']
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_performance_with_large_document(self):
        """Test system performance with large documents"""
        # Create a large document
        large_content = """# Large Document Test
        
This is a performance test document.

""" + "\n\n".join([f"""## Section {i}
This is section {i} with substantial content. """ + "Lorem ipsum dolor sit amet, consectetur adipiscing elit. " * 20 for i in range(50)])
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write(large_content)
            large_file_path = tmp_file.name
        
        try:
            # Mock services to avoid actual API calls
            with patch.object(azure_openai_service, 'get_embeddings') as mock_embeddings, \
                 patch.object(azure_openai_service, 'chat_completion') as mock_chat, \
                 patch.object(qdrant_service, 'client') as mock_qdrant:
                
                # Setup mocks for large document
                mock_embeddings.return_value = [[0.1] * 3072 for _ in range(100)]
                mock_chat.return_value = "Mocked response"
                mock_qdrant.upsert.return_value = True
                
                import time
                start_time = time.time()
                
                result = await document_processing_workflow.execute(
                    file_path=large_file_path,
                    file_name="large_document.txt",
                    document_type="RFP"
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Verify processing completed
                assert result['overall_status'] == 'success'
                
                # Check that processing time is reasonable (should be fast with mocks)
                assert processing_time < 30  # Should complete within 30 seconds with mocks
                
                # Verify large document was processed correctly
                parsing_result = result['parsing_result']
                assert len(parsing_result['structure']['headings']) >= 50  # Should find many headings
                
        finally:
            os.unlink(large_file_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "integration"])
