"""
Quality Assurance Agent for RFP/RFO document processing
Specialized in validating and ensuring quality of document processing results
"""
import logging
from typing import Dict, List, Any, Optional
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from services.azure_openai_service import azure_openai_service

logger = logging.getLogger(__name__)


class QualityAssuranceAgent:
    """Agent specialized in quality assurance and validation"""
    
    def __init__(self):
        """Initialize the quality assurance agent"""
        self.agent_name = "QualityAssuranceAgent"
        self.capabilities = [
            "document_validation",
            "prompt_quality_assessment",
            "completeness_verification",
            "accuracy_validation",
            "consistency_checking"
        ]
        
        self.system_message = """You are a specialized Quality Assurance Agent for RFP/RFO processing.
Your primary responsibilities are:

1. DOCUMENT PROCESSING VALIDATION:
   - Verify accuracy of document parsing and structure extraction
   - Validate completeness of content analysis
   - Check for missing or misinterpreted sections
   - Ensure proper categorization and classification

2. PROMPT QUALITY ASSESSMENT:
   - Evaluate generated prompts for clarity and specificity
   - Assess prompt completeness and actionability
   - Verify prompts address all relevant requirements
   - Check for consistency across related sections

3. CONTENT QUALITY VERIFICATION:
   - Validate extracted requirements and specifications
   - Check compliance analysis accuracy
   - Verify content categorization and tagging
   - Assess overall processing quality

4. CONSISTENCY AND ACCURACY:
   - Ensure consistent processing across document sections
   - Validate cross-references and relationships
   - Check for logical inconsistencies or gaps
   - Verify metadata accuracy and completeness

Provide detailed quality assessments with specific recommendations for improvement."""
        
        logger.info(f"Initialized {self.agent_name}")
    
    async def validate_document_processing(
        self,
        processing_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate the results of document processing
        
        Args:
            processing_results: Results from document processing pipeline
            
        Returns:
            Quality validation report
        """
        try:
            logger.info(f"{self.agent_name}: Validating document processing results")
            
            validation_report = {
                'overall_quality_score': 0.0,
                'validation_details': {},
                'issues_found': [],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': []
            }
            
            # Validate parsing results
            parsing_validation = await self._validate_parsing_quality(processing_results)
            validation_report['validation_details']['parsing'] = parsing_validation
            
            # Validate content analysis
            content_validation = await self._validate_content_analysis(processing_results)
            validation_report['validation_details']['content_analysis'] = content_validation
            
            # Validate prompt generation
            prompt_validation = await self._validate_prompt_quality(processing_results)
            validation_report['validation_details']['prompt_generation'] = prompt_validation
            
            # Calculate overall quality score
            scores = [
                parsing_validation.get('quality_score', 0),
                content_validation.get('quality_score', 0),
                prompt_validation.get('quality_score', 0)
            ]
            validation_report['overall_quality_score'] = sum(scores) / len(scores)
            
            # Compile issues and recommendations
            for validation in [parsing_validation, content_validation, prompt_validation]:
                validation_report['issues_found'].extend(validation.get('issues', []))
                validation_report['recommendations'].extend(validation.get('recommendations', []))
                validation_report['passed_checks'].extend(validation.get('passed_checks', []))
                validation_report['failed_checks'].extend(validation.get('failed_checks', []))
            
            return {
                'agent': self.agent_name,
                'validation_report': validation_report,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"{self.agent_name}: Error validating document processing: {str(e)}")
            return {
                'agent': self.agent_name,
                'status': 'error',
                'error': str(e)
            }
    
    async def _validate_parsing_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate document parsing quality"""
        try:
            validation = {
                'quality_score': 0.0,
                'issues': [],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': []
            }
            
            # Check if parsing was successful
            if results.get('parsing_status') == 'success':
                validation['passed_checks'].append("Document parsing completed successfully")
                score = 0.3
            else:
                validation['failed_checks'].append("Document parsing failed")
                validation['issues'].append("Document parsing was not successful")
                return validation
            
            # Check structure extraction
            structure = results.get('structure', {})
            headings = structure.get('headings', [])
            sections = structure.get('sections', [])
            
            if len(headings) > 0:
                validation['passed_checks'].append(f"Found {len(headings)} headings")
                score += 0.2
            else:
                validation['failed_checks'].append("No headings detected")
                validation['issues'].append("Document structure may not have been properly extracted")
            
            if len(sections) > 0:
                validation['passed_checks'].append(f"Identified {len(sections)} sections")
                score += 0.2
            else:
                validation['failed_checks'].append("No sections identified")
                validation['issues'].append("Document sections were not properly segmented")
            
            # Check content quality
            content = results.get('content', '')
            if len(content) > 100:
                validation['passed_checks'].append("Substantial content extracted")
                score += 0.2
            else:
                validation['failed_checks'].append("Insufficient content extracted")
                validation['issues'].append("Very little content was extracted from the document")
            
            # Check metadata
            metadata = results.get('metadata', {})
            if metadata.get('document_type') != 'unknown':
                validation['passed_checks'].append("Document type identified")
                score += 0.1
            else:
                validation['failed_checks'].append("Document type not identified")
                validation['recommendations'].append("Consider manual document type classification")
            
            validation['quality_score'] = score
            
            return validation
            
        except Exception as e:
            logger.error(f"Error validating parsing quality: {str(e)}")
            return {
                'quality_score': 0.0,
                'issues': [f"Parsing validation failed: {str(e)}"],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': ['Parsing validation error']
            }
    
    async def _validate_content_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate content analysis quality"""
        try:
            validation = {
                'quality_score': 0.0,
                'issues': [],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': []
            }
            
            # Check if content analysis was performed
            analysis_result = results.get('analysis_result')
            if not analysis_result:
                validation['failed_checks'].append("No content analysis performed")
                validation['issues'].append("Content analysis was not completed")
                return validation
            
            score = 0.0
            
            # Check for key analysis components
            if analysis_result.get('key_requirements'):
                validation['passed_checks'].append("Key requirements extracted")
                score += 0.25
            else:
                validation['failed_checks'].append("No key requirements extracted")
                validation['recommendations'].append("Review document for requirement extraction")
            
            if analysis_result.get('compliance_elements'):
                validation['passed_checks'].append("Compliance elements identified")
                score += 0.25
            else:
                validation['failed_checks'].append("No compliance elements identified")
                validation['recommendations'].append("Check for compliance and regulatory requirements")
            
            if analysis_result.get('executive_summary'):
                validation['passed_checks'].append("Executive summary generated")
                score += 0.25
            else:
                validation['failed_checks'].append("No executive summary generated")
                validation['recommendations'].append("Generate document summary for overview")
            
            # Check content quality score
            content_quality = analysis_result.get('content_quality_score', 0)
            if content_quality > 0.7:
                validation['passed_checks'].append("High content quality score")
                score += 0.25
            elif content_quality > 0.5:
                validation['passed_checks'].append("Moderate content quality score")
                score += 0.15
            else:
                validation['failed_checks'].append("Low content quality score")
                validation['recommendations'].append("Review document quality and completeness")
            
            validation['quality_score'] = score
            
            return validation
            
        except Exception as e:
            logger.error(f"Error validating content analysis: {str(e)}")
            return {
                'quality_score': 0.0,
                'issues': [f"Content analysis validation failed: {str(e)}"],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': ['Content analysis validation error']
            }
    
    async def _validate_prompt_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate prompt generation quality"""
        try:
            validation = {
                'quality_score': 0.0,
                'issues': [],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': []
            }
            
            # Check if prompts were generated
            section_prompts = results.get('section_prompts', {})
            if not section_prompts:
                validation['failed_checks'].append("No prompts generated")
                validation['issues'].append("Prompt generation was not completed")
                return validation
            
            score = 0.0
            prompt_count = len(section_prompts)
            
            # Check prompt quantity
            if prompt_count > 5:
                validation['passed_checks'].append(f"Generated {prompt_count} prompts")
                score += 0.3
            elif prompt_count > 0:
                validation['passed_checks'].append(f"Generated {prompt_count} prompts")
                score += 0.2
            else:
                validation['failed_checks'].append("No prompts generated")
                return validation
            
            # Check prompt quality
            quality_issues = 0
            for section, prompt_data in section_prompts.items():
                prompt = prompt_data.get('prompt', '')
                
                # Check prompt length (should be substantial)
                if len(prompt) < 100:
                    quality_issues += 1
                    validation['issues'].append(f"Prompt for '{section}' is too short")
                
                # Check for actionable language
                actionable_words = ['create', 'provide', 'include', 'describe', 'explain', 'detail']
                if not any(word in prompt.lower() for word in actionable_words):
                    quality_issues += 1
                    validation['issues'].append(f"Prompt for '{section}' lacks actionable language")
            
            # Calculate quality score based on issues
            if quality_issues == 0:
                validation['passed_checks'].append("All prompts meet quality standards")
                score += 0.4
            elif quality_issues < prompt_count * 0.3:
                validation['passed_checks'].append("Most prompts meet quality standards")
                score += 0.3
            else:
                validation['failed_checks'].append("Many prompts have quality issues")
                validation['recommendations'].append("Review and improve prompt generation")
                score += 0.1
            
            # Check for prompt diversity
            unique_prompts = set(prompt_data.get('prompt', '') for prompt_data in section_prompts.values())
            if len(unique_prompts) == len(section_prompts):
                validation['passed_checks'].append("Prompts are diverse and section-specific")
                score += 0.3
            else:
                validation['failed_checks'].append("Some prompts are duplicated or too similar")
                validation['recommendations'].append("Ensure prompts are tailored to specific sections")
                score += 0.1
            
            validation['quality_score'] = score
            
            return validation
            
        except Exception as e:
            logger.error(f"Error validating prompt quality: {str(e)}")
            return {
                'quality_score': 0.0,
                'issues': [f"Prompt validation failed: {str(e)}"],
                'recommendations': [],
                'passed_checks': [],
                'failed_checks': ['Prompt validation error']
            }
    
    async def generate_quality_report(
        self,
        validation_results: Dict[str, Any]
    ) -> str:
        """
        Generate a comprehensive quality report
        
        Args:
            validation_results: Results from quality validation
            
        Returns:
            Formatted quality report
        """
        try:
            validation_report = validation_results.get('validation_report', {})
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Generate a comprehensive quality report based on these validation results:

OVERALL QUALITY SCORE: {validation_report.get('overall_quality_score', 0):.2f}

PASSED CHECKS:
{chr(10).join('- ' + check for check in validation_report.get('passed_checks', []))}

FAILED CHECKS:
{chr(10).join('- ' + check for check in validation_report.get('failed_checks', []))}

ISSUES FOUND:
{chr(10).join('- ' + issue for issue in validation_report.get('issues_found', []))}

RECOMMENDATIONS:
{chr(10).join('- ' + rec for rec in validation_report.get('recommendations', []))}

Please provide:
1. EXECUTIVE SUMMARY of quality assessment
2. KEY STRENGTHS identified
3. CRITICAL ISSUES that need attention
4. PRIORITY RECOMMENDATIONS for improvement
5. OVERALL ASSESSMENT and next steps

Format as a professional quality assurance report.""")
            ]
            
            quality_report = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.3
            )
            
            return quality_report
            
        except Exception as e:
            logger.error(f"Error generating quality report: {str(e)}")
            return f"Quality Report Generation Failed: {str(e)}"
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and capabilities"""
        return {
            'agent_name': self.agent_name,
            'status': 'active',
            'capabilities': self.capabilities,
            'version': '1.0.0'
        }
