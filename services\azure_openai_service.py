"""
Azure OpenAI Service for embeddings and chat completions
"""
import os
import logging
from typing import List, Dict, Any, Optional
import asyncio
from openai import AsyncAzureOpenAI
from config.settings import (
    AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_DEPLOYMENT_NAME,
    AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_EMBEDDING_ENDPOINT,
    AZURE_OPENAI_EMBEDDING_API_KEY,
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME,
    AZURE_OPENAI_EMBEDDING_API_VERSION,
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    EMBEDDING_MODEL
)

logger = logging.getLogger(__name__)


class AzureOpenAIService:
    """Service for Azure OpenAI operations including embeddings and chat completions"""
    
    def __init__(self):
        """Initialize Azure OpenAI clients for chat and embeddings"""
        # Chat completion client
        if not AZURE_OPENAI_ENDPOINT or not AZURE_OPENAI_API_KEY:
            raise ValueError("Azure OpenAI credentials not found in environment variables")

        self.client = AsyncAzureOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_API_KEY,
            api_version=AZURE_OPENAI_API_VERSION
        )

        # Embedding client (separate endpoint and key)
        if not AZURE_OPENAI_EMBEDDING_ENDPOINT or not AZURE_OPENAI_EMBEDDING_API_KEY:
            raise ValueError("Azure OpenAI embedding credentials not found in environment variables")

        self.embedding_client = AsyncAzureOpenAI(
            azure_endpoint=AZURE_OPENAI_EMBEDDING_ENDPOINT,
            api_key=AZURE_OPENAI_EMBEDDING_API_KEY,
            api_version=AZURE_OPENAI_EMBEDDING_API_VERSION
        )

        self.deployment_name = AZURE_OPENAI_DEPLOYMENT_NAME
        self.embedding_deployment = AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME

        logger.info(f"Azure OpenAI service initialized with chat endpoint: {AZURE_OPENAI_ENDPOINT}")
        logger.info(f"Azure OpenAI service initialized with embedding endpoint: {AZURE_OPENAI_EMBEDDING_ENDPOINT}")
    
    async def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for a list of texts using Azure OpenAI
        
        Args:
            texts: List of text strings to embed
            
        Returns:
            List of embedding vectors
        """
        try:
            # Azure OpenAI embeddings API call using separate embedding client
            response = await self.embedding_client.embeddings.create(
                model=self.embedding_deployment,
                input=texts
            )
            
            embeddings = [data.embedding for data in response.data]
            logger.info(f"Generated embeddings for {len(texts)} texts")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise
    
    async def get_single_embedding(self, text: str) -> List[float]:
        """
        Get embedding for a single text
        
        Args:
            text: Text string to embed
            
        Returns:
            Embedding vector
        """
        embeddings = await self.get_embeddings([text])
        return embeddings[0]
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Get chat completion from Azure OpenAI
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
            
        Returns:
            Generated response text
        """
        try:
            response = await self.client.chat.completions.create(
                model=self.deployment_name,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error in chat completion: {str(e)}")
            raise
    
    async def analyze_document_structure(self, content: str) -> Dict[str, Any]:
        """
        Analyze document structure using Azure OpenAI
        
        Args:
            content: Document content to analyze
            
        Returns:
            Dictionary containing structure analysis
        """
        messages = [
            {
                "role": "system",
                "content": """You are a document structure analyzer. Analyze the given document content and extract:
1. Main headings and their hierarchy levels
2. Section structure and organization
3. Key topics and themes
4. Document type classification
5. Table of contents structure

Return your analysis in a structured JSON format."""
            },
            {
                "role": "user",
                "content": f"Analyze this document structure:\n\n{content[:4000]}"  # Limit content length
            }
        ]
        
        try:
            response = await self.chat_completion(messages, temperature=0.3)
            # Parse the JSON response
            import json
            try:
                structure = json.loads(response)
                return structure
            except json.JSONDecodeError:
                # If JSON parsing fails, return a basic structure
                return {
                    "analysis": response,
                    "headings": [],
                    "sections": [],
                    "document_type": "unknown"
                }
                
        except Exception as e:
            logger.error(f"Error analyzing document structure: {str(e)}")
            return {
                "error": str(e),
                "headings": [],
                "sections": [],
                "document_type": "unknown"
            }
    
    async def generate_contextual_prompt(
        self,
        heading: str,
        context: str,
        document_type: str = "RFP"
    ) -> str:
        """
        Generate a contextual prompt for a specific heading
        
        Args:
            heading: The heading/section title
            context: Surrounding context from the document
            document_type: Type of document (RFP, RFO, etc.)
            
        Returns:
            Generated prompt text
        """
        messages = [
            {
                "role": "system",
                "content": f"""You are an expert {document_type} prompt generator. Given a heading and its context, 
generate a detailed, actionable prompt that would help someone create comprehensive content for that section.
The prompt should be specific, clear, and tailored to the {document_type} context."""
            },
            {
                "role": "user",
                "content": f"""Generate a prompt for this section:
Heading: {heading}
Context: {context[:1000]}
Document Type: {document_type}

Create a prompt that guides content creation for this specific section."""
            }
        ]
        
        try:
            prompt = await self.chat_completion(messages, temperature=0.7)
            return prompt
            
        except Exception as e:
            logger.error(f"Error generating contextual prompt: {str(e)}")
            return f"Generate detailed content for the '{heading}' section of this {document_type}."


# Global instance
azure_openai_service = AzureOpenAIService()
