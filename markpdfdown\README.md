## Overview

MarkPDFDown is designed to simplify the process of converting PDF documents into clean, editable Markdown text. By utilizing advanced multimodal AI models, it can accurately extract text, preserve formatting, and handle complex document structures including tables, formulas, and diagrams.

## Features

- **PDF to Markdown Conversion**: Transform any PDF document into well-formatted Markdown
- **Image to Markdown Conversion**: Transform image into well-formatted Markdown
- **Multimodal Understanding**: Leverages AI to comprehend document structure and content
- **Format Preservation**: Maintains headings, lists, tables, and other formatting elements
- **Customizable Model**: Configure the model to suit your needs

## Installation

### Using uv (Recommended)

```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone the repository
git clone https://github.com/MarkPDFdown/markpdfdown.git
cd markpdfdown

# Install dependencies and create virtual environment
uv sync

```

### Using conda

```bash
conda create -n markpdfdown python=3.9
conda activate markpdfdown

# Clone the repository
git clone https://github.com/MarkPDFdown/markpdfdown.git
cd markpdfdown

# Install dependencies
pip install -e .
```
## Usage

### Configuration

Before using MarkPDFDown, you need to configure your Azure OpenAI credentials. Copy the example environment file and update it with your Azure OpenAI details:

```bash
cp .env.example .env
```

Edit the `.env` file with your Azure OpenAI configuration:

```bash
# Your Azure OpenAI API key
AZURE_OPENAI_API_KEY="your_azure_openai_api_key_here"

# Your Azure OpenAI endpoint URL
AZURE_OPENAI_ENDPOINT="https://your-resource-name.openai.azure.com/"

# The deployment name of your model in Azure OpenAI
AZURE_OPENAI_DEPLOYMENT_NAME="your_deployment_name_here"

# API version (optional, defaults to 2024-02-01)
AZURE_OPENAI_API_VERSION="2024-02-01"
```

### Basic Usage

```bash
# PDF to markdown
python main.py < tests/input.pdf > output.md

# Image to markdown
python main.py < input_image.png > output.md
```

## Advanced Usage

### Page Range Selection

```bash
# Process only first 5 pages
python main.py 5 < tests/input.pdf > output.md

# Process pages 3 to 10
python main.py 3 10 < tests/input.pdf > output.md
```

### Resume Interrupted Processing

The tool now supports resuming from interrupted processing sessions:

```bash
# List available output directories that can be resumed
python main.py --list-dirs

# Resume processing from a specific output directory
python main.py --resume output/20250627144306/

# Resume processing and keep the output files (don't clean up)
python main.py --resume output/20250627144306/ --no-cleanup

# Keep output files for debugging (works with any processing)
python main.py --no-cleanup < input.pdf > output.md
```

**How Resume Works:**
- The tool checks the specified output directory for existing `.md` files
- It skips processing images that already have corresponding markdown files
- Only processes new or missing pages
- Combines all content (existing + new) in the final output

This is particularly useful for:
- Large PDFs that might timeout or fail partway through
- Debugging specific pages without reprocessing the entire document
- Continuing work after interruption

### Save and Cleanup Options

New options for managing output files:

```bash
# Save combined markdown to a specific file
python main.py --output final_document.md < input.pdf

# Save to file and automatically clean up all intermediate files
python main.py --save-and-cleanup < input.pdf

# Resume processing, save to file, and clean up intermediate files
python main.py --resume output/20250627144306/ --save-and-cleanup

# Specify custom output filename
python main.py --resume output/20250627144306/ --output my_document.md
```

**Save and Cleanup Features:**
- `--output filename.md`: Save combined markdown to specified file (also outputs to stdout)
- `--save-and-cleanup`: Save to auto-generated filename and clean up intermediate files (preserves final output)
- Automatic filename generation based on input file or directory name
- Safe cleanup: only removes intermediate files, preserves the final combined markdown
- Smart file placement: ensures final output is saved outside temporary directories

## Docker Usage

```bash
docker run -i \
  -e AZURE_OPENAI_API_KEY=your_api_key \
  -e AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/ \
  -e AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name \
  jorbenzhu/markpdfdown < input.pdf > output.md
```

## Development Setup

### Code Quality Tools

This project uses `ruff` for linting and formatting, and `pre-commit` for automated code quality checks.

#### Install development dependencies

```bash
# If using uv
uv sync --group dev

# If using pip
pip install -e ".[dev]"
```

#### Set up pre-commit hooks

```bash
# Install pre-commit hooks
pre-commit install

# Run pre-commit on all files (optional)
pre-commit run --all-files
```

#### Code formatting and linting

```bash
# Format code with ruff
ruff format

# Run linting checks
ruff check

# Fix auto-fixable issues
ruff check --fix
```

## Requirements
- Python 3.9+
- [uv](https://astral.sh/uv/) (recommended for package management) or conda/pip
- Dependencies specified in `pyproject.toml`
- Access to the specified multimodal AI model

## Contributing
Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch ( `git checkout -b feature/amazing-feature` )
3. Set up the development environment:
   ```bash
   uv sync --group dev
   pre-commit install
   ```
4. Make your changes and ensure code quality:
   ```bash
   ruff format
   ruff check --fix
   pre-commit run --all-files
   ```
5. Commit your changes ( `git commit -m 'feat: Add some amazing feature'` )
6. Push to the branch ( `git push origin feature/amazing-feature` )
7. Open a Pull Request

Please ensure your code follows the project's coding standards by running the linting and formatting tools before submitting.

## License
This project is licensed under the Apache License 2.0. See the LICENSE file for details.

## Acknowledgments
- Thanks to the developers of the multimodal AI models that power this tool
- Inspired by the need for better PDF to Markdown conversion tools

[hub_url]: https://hub.docker.com/r/jorbenzhu/markpdfdown/
[tag_url]: https://github.com/markpdfdown/markpdfdown/releases
[license_url]: https://github.com/markpdfdown/markpdfdown/blob/main/LICENSE

[Size]: https://img.shields.io/docker/image-size/jorbenzhu/markpdfdown/latest?color=066da5&label=size
[Pulls]: https://img.shields.io/docker/pulls/jorbenzhu/markpdfdown.svg?style=flat&label=pulls&logo=docker
[Tag]: https://img.shields.io/github/release/markpdfdown/markpdfdown.svg
[License]: https://img.shields.io/github/license/markpdfdown/markpdfdown
