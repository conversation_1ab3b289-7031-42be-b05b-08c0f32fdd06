# Azure OpenAI Configuration
# Replace these values with your actual Azure OpenAI resource details

# Your Azure OpenAI API key (found in Azure portal under your OpenAI resource)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here

# Your Azure OpenAI endpoint URL (found in Azure portal under your OpenAI resource)
# Format: https://your-resource-name.openai.azure.com/
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/

# The deployment name of your model in Azure OpenAI Studio
# This is the name you gave when deploying the model (e.g., "gpt-4o-deployment")
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name_here

# Azure OpenAI API version (optional, defaults to 2024-02-01)
AZURE_OPENAI_API_VERSION=2024-02-01

# Legacy OpenAI environment variables (no longer used with Azure OpenAI)
# OPENAI_API_KEY=
# OPENAI_API_BASE=
# OPENAI_DEFAULT_MODEL=
