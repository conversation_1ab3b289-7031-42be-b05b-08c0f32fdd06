"""
Test Runner for RFP/RFO Document Processing System
Comprehensive test execution with reporting and coverage
"""
import pytest
import sys
import os
import subprocess
import argparse
from datetime import datetime
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests"""
    print("🧪 Running Unit Tests...")
    
    args = [
        "pytest",
        "tests/test_azure_openai_service.py",
        "tests/test_qdrant_service.py", 
        "tests/test_document_processor.py",
        "tests/test_document_processing_workflow.py",
        "-v" if verbose else "",
        "--tb=short"
    ]
    
    if coverage:
        args.extend(["--cov=services", "--cov=agents", "--cov=workflows", "--cov-report=html"])
    
    # Filter out empty strings
    args = [arg for arg in args if arg]
    
    result = subprocess.run(args, capture_output=True, text=True)
    
    print(f"Unit Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
    if result.returncode != 0:
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
    
    return result.returncode == 0


def run_integration_tests(verbose=False):
    """Run integration tests"""
    print("🔗 Running Integration Tests...")
    
    args = [
        "pytest",
        "tests/test_integration.py",
        "-m", "integration",
        "-v" if verbose else "",
        "--tb=short"
    ]
    
    # Filter out empty strings
    args = [arg for arg in args if arg]
    
    result = subprocess.run(args, capture_output=True, text=True)
    
    print(f"Integration Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
    if result.returncode != 0:
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
    
    return result.returncode == 0


def run_agent_tests(verbose=False):
    """Run agent-specific tests"""
    print("🤖 Running Agent Tests...")
    
    # Test individual agents
    agent_test_files = [
        "tests/test_approval_agent.py"  # Existing test file
    ]
    
    # Check which test files exist
    existing_files = [f for f in agent_test_files if os.path.exists(f)]
    
    if not existing_files:
        print("⚠️ No agent test files found")
        return True
    
    args = ["pytest"] + existing_files + (["-v"] if verbose else []) + ["--tb=short"]
    
    result = subprocess.run(args, capture_output=True, text=True)
    
    print(f"Agent Tests Result: {'✅ PASSED' if result.returncode == 0 else '❌ FAILED'}")
    if result.returncode != 0:
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
    
    return result.returncode == 0


def check_dependencies():
    """Check if all required dependencies are available"""
    print("📦 Checking Dependencies...")
    
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'pytest-cov',
        'langchain',
        'langgraph',
        'openai',
        'streamlit',
        'qdrant-client'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies available")
    return True


def check_environment():
    """Check environment configuration"""
    print("🔧 Checking Environment Configuration...")
    
    required_env_vars = [
        'AZURE_OPENAI_ENDPOINT',
        'AZURE_OPENAI_API_KEY',
        'AZURE_OPENAI_DEPLOYMENT_NAME'
    ]
    
    missing_vars = []
    
    # Load from markpdfdown/.env if exists
    env_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'markpdfdown', '.env')
    if os.path.exists(env_file):
        print(f"📄 Loading environment from {env_file}")
        from dotenv import load_dotenv
        load_dotenv(env_file)
    
    for var in required_env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var} - SET")
        else:
            print(f"❌ {var} - NOT SET")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ Missing environment variables: {', '.join(missing_vars)}")
        print("Set these in markpdfdown/.env file")
        return False
    
    print("✅ Environment configuration complete")
    return True


def run_system_health_check():
    """Run system health check"""
    print("🏥 Running System Health Check...")
    
    try:
        # Test imports
        from services.azure_openai_service import azure_openai_service
        from services.qdrant_service import qdrant_service
        from services.document_processor import document_processor
        from workflows.document_processing_workflow import document_processing_workflow
        
        print("✅ All modules import successfully")
        
        # Test workflow status
        status = document_processing_workflow.get_workflow_status()
        if status['status'] == 'active':
            print("✅ Workflow is active")
        else:
            print("❌ Workflow is not active")
            return False
        
        # Check agent count
        if len(status['agents']) == 4:
            print("✅ All 4 agents are available")
        else:
            print(f"❌ Expected 4 agents, found {len(status['agents'])}")
            return False
        
        print("✅ System health check passed")
        return True
        
    except Exception as e:
        print(f"❌ System health check failed: {str(e)}")
        return False


def generate_test_report(results):
    """Generate test report"""
    print("\n📊 Generating Test Report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'results': results,
        'summary': {
            'total_tests': len(results),
            'passed': sum(1 for r in results.values() if r),
            'failed': sum(1 for r in results.values() if not r)
        }
    }
    
    # Save report
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"📄 Test report saved to: {report_file}")
    
    # Print summary
    print("\n📋 Test Summary:")
    print(f"Total Test Suites: {report['summary']['total_tests']}")
    print(f"Passed: {report['summary']['passed']} ✅")
    print(f"Failed: {report['summary']['failed']} ❌")
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    return report


def run_sample_document_test():
    """Run test with sample RFP document"""
    print("📄 Running Sample Document Test...")
    
    try:
        # Check if sample documents exist
        sample_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'RFP_sample')
        
        if not os.path.exists(sample_dir):
            print("⚠️ Sample documents directory not found")
            return True
        
        sample_files = [f for f in os.listdir(sample_dir) if f.endswith(('.pdf', '.txt', '.md'))]
        
        if not sample_files:
            print("⚠️ No sample documents found")
            return True
        
        print(f"📁 Found {len(sample_files)} sample documents")
        
        # Test with first sample document (mock mode)
        sample_file = os.path.join(sample_dir, sample_files[0])
        print(f"🧪 Testing with: {sample_files[0]}")
        
        # This would be a real test in production
        # For now, just verify the file exists and is readable
        with open(sample_file, 'rb') as f:
            content = f.read()
            if len(content) > 0:
                print("✅ Sample document is readable")
                return True
            else:
                print("❌ Sample document is empty")
                return False
        
    except Exception as e:
        print(f"❌ Sample document test failed: {str(e)}")
        return False


def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description='RFP/RFO Document Processing System Test Runner')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--coverage', '-c', action='store_true', help='Generate coverage report')
    parser.add_argument('--unit-only', action='store_true', help='Run only unit tests')
    parser.add_argument('--integration-only', action='store_true', help='Run only integration tests')
    parser.add_argument('--health-check', action='store_true', help='Run only health check')
    parser.add_argument('--skip-deps', action='store_true', help='Skip dependency check')
    
    args = parser.parse_args()
    
    print("🚀 RFP/RFO Document Processing System Test Runner")
    print("=" * 60)
    
    results = {}
    
    # Dependency check
    if not args.skip_deps:
        if not check_dependencies():
            print("❌ Dependency check failed. Exiting.")
            return 1
        results['dependencies'] = True
    
    # Environment check
    env_ok = check_environment()
    results['environment'] = env_ok
    
    # Health check
    if args.health_check:
        health_ok = run_system_health_check()
        results['health_check'] = health_ok
        generate_test_report(results)
        return 0 if health_ok else 1
    
    # System health check
    health_ok = run_system_health_check()
    results['health_check'] = health_ok
    
    if not health_ok:
        print("⚠️ System health check failed, but continuing with tests...")
    
    # Run tests based on arguments
    if args.unit_only:
        results['unit_tests'] = run_unit_tests(args.verbose, args.coverage)
    elif args.integration_only:
        results['integration_tests'] = run_integration_tests(args.verbose)
    else:
        # Run all tests
        results['unit_tests'] = run_unit_tests(args.verbose, args.coverage)
        results['agent_tests'] = run_agent_tests(args.verbose)
        results['integration_tests'] = run_integration_tests(args.verbose)
        results['sample_document_test'] = run_sample_document_test()
    
    # Generate report
    report = generate_test_report(results)
    
    # Return exit code
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
