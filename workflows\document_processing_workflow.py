"""
Document Processing Workflow - Multi-agent orchestration for RFP/RFO document processing
"""
import logging
from typing import Dict, Any, List, TypedDict, Union
from enum import Enum
import uuid
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.graph import add_messages

from agents.document_parsing_agent import DocumentParsingAgent
from agents.content_analysis_agent import ContentAnalysisAgent
from agents.prompt_generation_agent import PromptGenerationAgent
from agents.quality_assurance_agent import QualityAssuranceAgent

logger = logging.getLogger(__name__)


# Define the state schema for the document processing workflow
class DocumentProcessingState(TypedDict):
    # Input
    file_path: str
    file_name: str
    document_type: str
    
    # Processing results
    parsing_result: Union[Dict[str, Any], None]
    content_analysis: Union[Dict[str, Any], None]
    prompt_generation: Union[Dict[str, Any], None]
    quality_validation: Union[Dict[str, Any], None]
    
    # Workflow state
    current_step: str
    error: Union[str, None]
    status: str
    workflow_id: str
    
    # Final output
    final_result: Union[Dict[str, Any], None]


# Define the node names for clarity
class ProcessingNode(str, Enum):
    PARSE_DOCUMENT = "parse_document"
    ANALYZE_CONTENT = "analyze_content"
    GENERATE_PROMPTS = "generate_prompts"
    VALIDATE_QUALITY = "validate_quality"
    FINALIZE_RESULTS = "finalize_results"
    ERROR_HANDLER = "error_handler"


class DocumentProcessingWorkflow:
    """Multi-agent workflow for comprehensive document processing"""
    
    def __init__(self):
        """Initialize the document processing workflow with all agents"""
        self.workflow_name = "DocumentProcessingWorkflow"
        
        # Initialize all agents
        self.parsing_agent = DocumentParsingAgent()
        self.content_agent = ContentAnalysisAgent()
        self.prompt_agent = PromptGenerationAgent()
        self.qa_agent = QualityAssuranceAgent()
        
        # Build the workflow graph
        self.graph = self._build_workflow()
        
        logger.info(f"Initialized {self.workflow_name} with multi-agent orchestration")
    
    def _build_workflow(self) -> StateGraph:
        """
        Build the multi-agent workflow graph
        
        Returns:
            StateGraph: The workflow graph
        """
        # Create workflow graph
        graph = StateGraph(DocumentProcessingState)
        
        # Add nodes for each processing step
        graph.add_node(ProcessingNode.PARSE_DOCUMENT, self._parse_document_step)
        graph.add_node(ProcessingNode.ANALYZE_CONTENT, self._analyze_content_step)
        graph.add_node(ProcessingNode.GENERATE_PROMPTS, self._generate_prompts_step)
        graph.add_node(ProcessingNode.VALIDATE_QUALITY, self._validate_quality_step)
        graph.add_node(ProcessingNode.FINALIZE_RESULTS, self._finalize_results_step)
        graph.add_node(ProcessingNode.ERROR_HANDLER, self._handle_error_step)
        
        # Define the workflow edges
        graph.set_entry_point(ProcessingNode.PARSE_DOCUMENT)
        
        # Sequential processing with conditional routing
        graph.add_conditional_edges(
            ProcessingNode.PARSE_DOCUMENT,
            self._route_after_parsing,
            {
                "success": ProcessingNode.ANALYZE_CONTENT,
                "error": ProcessingNode.ERROR_HANDLER
            }
        )
        
        graph.add_conditional_edges(
            ProcessingNode.ANALYZE_CONTENT,
            self._route_after_analysis,
            {
                "success": ProcessingNode.GENERATE_PROMPTS,
                "error": ProcessingNode.ERROR_HANDLER
            }
        )
        
        graph.add_conditional_edges(
            ProcessingNode.GENERATE_PROMPTS,
            self._route_after_prompts,
            {
                "success": ProcessingNode.VALIDATE_QUALITY,
                "error": ProcessingNode.ERROR_HANDLER
            }
        )
        
        graph.add_conditional_edges(
            ProcessingNode.VALIDATE_QUALITY,
            self._route_after_validation,
            {
                "success": ProcessingNode.FINALIZE_RESULTS,
                "error": ProcessingNode.ERROR_HANDLER
            }
        )
        
        # End points
        graph.add_edge(ProcessingNode.FINALIZE_RESULTS, END)
        graph.add_edge(ProcessingNode.ERROR_HANDLER, END)
        
        return graph
    
    async def _parse_document_step(self, state: DocumentProcessingState) -> DocumentProcessingState:
        """Document parsing step using DocumentParsingAgent"""
        try:
            logger.info(f"Starting document parsing for {state['file_name']}")
            
            # Parse document using the parsing agent
            parsing_result = await self.parsing_agent.parse_document(
                file_path=state['file_path'],
                file_name=state['file_name'],
                document_type=state['document_type']
            )
            
            # Update state
            updated_state = {
                **state,
                "parsing_result": parsing_result,
                "current_step": ProcessingNode.PARSE_DOCUMENT,
                "status": "Document parsing completed"
            }
            
            if parsing_result.get('parsing_status') == 'error':
                updated_state["error"] = parsing_result.get('error', 'Unknown parsing error')
                updated_state["status"] = "Document parsing failed"
            
            return updated_state
            
        except Exception as e:
            logger.error(f"Error in document parsing step: {str(e)}")
            return {
                **state,
                "error": f"Document parsing error: {str(e)}",
                "status": "Document parsing failed",
                "current_step": ProcessingNode.PARSE_DOCUMENT
            }
    
    async def _analyze_content_step(self, state: DocumentProcessingState) -> DocumentProcessingState:
        """Content analysis step using ContentAnalysisAgent"""
        try:
            logger.info("Starting content analysis")
            
            parsing_result = state.get('parsing_result', {})
            document_id = parsing_result.get('document_id')
            
            if not document_id:
                raise ValueError("No document ID available from parsing step")
            
            # Analyze content using the content analysis agent
            content_analysis = await self.content_agent.analyze_document_content(
                document_id=document_id,
                analysis_type="comprehensive"
            )
            
            # Update state
            updated_state = {
                **state,
                "content_analysis": content_analysis,
                "current_step": ProcessingNode.ANALYZE_CONTENT,
                "status": "Content analysis completed"
            }
            
            if content_analysis.get('status') == 'error':
                updated_state["error"] = content_analysis.get('error', 'Unknown analysis error')
                updated_state["status"] = "Content analysis failed"
            
            return updated_state
            
        except Exception as e:
            logger.error(f"Error in content analysis step: {str(e)}")
            return {
                **state,
                "error": f"Content analysis error: {str(e)}",
                "status": "Content analysis failed",
                "current_step": ProcessingNode.ANALYZE_CONTENT
            }
    
    async def _generate_prompts_step(self, state: DocumentProcessingState) -> DocumentProcessingState:
        """Prompt generation step using PromptGenerationAgent"""
        try:
            logger.info("Starting prompt generation")
            
            parsing_result = state.get('parsing_result', {})
            document_id = parsing_result.get('document_id')
            
            if not document_id:
                raise ValueError("No document ID available for prompt generation")
            
            # Generate prompts using the prompt generation agent
            prompt_generation = await self.prompt_agent.generate_section_prompts(
                document_id=document_id
            )
            
            # Update state
            updated_state = {
                **state,
                "prompt_generation": prompt_generation,
                "current_step": ProcessingNode.GENERATE_PROMPTS,
                "status": "Prompt generation completed"
            }
            
            if prompt_generation.get('status') == 'error':
                updated_state["error"] = prompt_generation.get('error', 'Unknown prompt generation error')
                updated_state["status"] = "Prompt generation failed"
            
            return updated_state
            
        except Exception as e:
            logger.error(f"Error in prompt generation step: {str(e)}")
            return {
                **state,
                "error": f"Prompt generation error: {str(e)}",
                "status": "Prompt generation failed",
                "current_step": ProcessingNode.GENERATE_PROMPTS
            }
    
    async def _validate_quality_step(self, state: DocumentProcessingState) -> DocumentProcessingState:
        """Quality validation step using QualityAssuranceAgent"""
        try:
            logger.info("Starting quality validation")
            
            # Compile all processing results for validation
            processing_results = {
                'parsing_result': state.get('parsing_result', {}),
                'content_analysis': state.get('content_analysis', {}),
                'prompt_generation': state.get('prompt_generation', {}),
                'parsing_status': state.get('parsing_result', {}).get('parsing_status'),
                'structure': state.get('parsing_result', {}).get('structure'),
                'content': state.get('parsing_result', {}).get('content'),
                'metadata': state.get('parsing_result', {}).get('metadata'),
                'analysis_result': state.get('content_analysis', {}).get('analysis_result'),
                'section_prompts': state.get('prompt_generation', {}).get('section_prompts')
            }
            
            # Validate quality using the QA agent
            quality_validation = await self.qa_agent.validate_document_processing(
                processing_results=processing_results
            )
            
            # Update state
            updated_state = {
                **state,
                "quality_validation": quality_validation,
                "current_step": ProcessingNode.VALIDATE_QUALITY,
                "status": "Quality validation completed"
            }
            
            if quality_validation.get('status') == 'error':
                updated_state["error"] = quality_validation.get('error', 'Unknown validation error')
                updated_state["status"] = "Quality validation failed"
            
            return updated_state
            
        except Exception as e:
            logger.error(f"Error in quality validation step: {str(e)}")
            return {
                **state,
                "error": f"Quality validation error: {str(e)}",
                "status": "Quality validation failed",
                "current_step": ProcessingNode.VALIDATE_QUALITY
            }
    
    async def _finalize_results_step(self, state: DocumentProcessingState) -> DocumentProcessingState:
        """Finalize and compile all results"""
        try:
            logger.info("Finalizing processing results")
            
            # Compile final results
            final_result = {
                'workflow_id': state['workflow_id'],
                'document_info': {
                    'file_name': state['file_name'],
                    'document_type': state['document_type'],
                    'processed_at': datetime.now().isoformat()
                },
                'parsing_result': state.get('parsing_result', {}),
                'content_analysis': state.get('content_analysis', {}),
                'prompt_generation': state.get('prompt_generation', {}),
                'quality_validation': state.get('quality_validation', {}),
                'overall_status': 'success',
                'processing_summary': self._generate_processing_summary(state)
            }
            
            return {
                **state,
                "final_result": final_result,
                "current_step": ProcessingNode.FINALIZE_RESULTS,
                "status": "Document processing completed successfully"
            }
            
        except Exception as e:
            logger.error(f"Error finalizing results: {str(e)}")
            return {
                **state,
                "error": f"Result finalization error: {str(e)}",
                "status": "Result finalization failed",
                "current_step": ProcessingNode.FINALIZE_RESULTS
            }
    
    async def _handle_error_step(self, state: DocumentProcessingState) -> DocumentProcessingState:
        """Handle errors in the workflow"""
        try:
            error_message = state.get('error', 'Unknown error occurred')
            logger.error(f"Workflow error: {error_message}")
            
            # Create error result
            final_result = {
                'workflow_id': state['workflow_id'],
                'document_info': {
                    'file_name': state['file_name'],
                    'document_type': state['document_type'],
                    'processed_at': datetime.now().isoformat()
                },
                'overall_status': 'error',
                'error': error_message,
                'failed_at_step': state.get('current_step', 'unknown'),
                'partial_results': {
                    'parsing_result': state.get('parsing_result'),
                    'content_analysis': state.get('content_analysis'),
                    'prompt_generation': state.get('prompt_generation'),
                    'quality_validation': state.get('quality_validation')
                }
            }
            
            return {
                **state,
                "final_result": final_result,
                "current_step": ProcessingNode.ERROR_HANDLER,
                "status": f"Workflow failed: {error_message}"
            }
            
        except Exception as e:
            logger.error(f"Error in error handler: {str(e)}")
            return {
                **state,
                "status": f"Critical workflow failure: {str(e)}"
            }
    
    def _generate_processing_summary(self, state: DocumentProcessingState) -> Dict[str, Any]:
        """Generate a summary of the processing results"""
        try:
            parsing_result = state.get('parsing_result', {})
            content_analysis = state.get('content_analysis', {})
            prompt_generation = state.get('prompt_generation', {})
            quality_validation = state.get('quality_validation', {})
            
            summary = {
                'document_parsed': parsing_result.get('parsing_status') == 'success',
                'sections_found': len(parsing_result.get('structure', {}).get('sections', [])),
                'headings_found': len(parsing_result.get('structure', {}).get('headings', [])),
                'content_analyzed': content_analysis.get('status') == 'success',
                'prompts_generated': prompt_generation.get('total_prompts', 0),
                'quality_score': quality_validation.get('validation_report', {}).get('overall_quality_score', 0),
                'stored_in_database': parsing_result.get('stored_in_db', False)
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating processing summary: {str(e)}")
            return {'error': str(e)}
    
    # Routing functions
    def _route_after_parsing(self, state: DocumentProcessingState) -> str:
        """Route after document parsing"""
        parsing_result = state.get('parsing_result', {})
        return "success" if parsing_result.get('parsing_status') == 'success' else "error"
    
    def _route_after_analysis(self, state: DocumentProcessingState) -> str:
        """Route after content analysis"""
        content_analysis = state.get('content_analysis', {})
        return "success" if content_analysis.get('status') == 'success' else "error"
    
    def _route_after_prompts(self, state: DocumentProcessingState) -> str:
        """Route after prompt generation"""
        prompt_generation = state.get('prompt_generation', {})
        return "success" if prompt_generation.get('status') == 'success' else "error"
    
    def _route_after_validation(self, state: DocumentProcessingState) -> str:
        """Route after quality validation"""
        quality_validation = state.get('quality_validation', {})
        return "success" if quality_validation.get('status') == 'success' else "error"

    async def execute(
        self,
        file_path: str,
        file_name: str,
        document_type: str = "RFP"
    ) -> Dict[str, Any]:
        """
        Execute the complete document processing workflow

        Args:
            file_path: Path to the document file
            file_name: Original filename
            document_type: Type of document (RFP, RFO, etc.)

        Returns:
            Complete processing results
        """
        try:
            logger.info(f"Starting document processing workflow for {file_name}")

            # Initialize workflow state
            workflow_id = str(uuid.uuid4())
            initial_state: DocumentProcessingState = {
                "file_path": file_path,
                "file_name": file_name,
                "document_type": document_type,
                "parsing_result": None,
                "content_analysis": None,
                "prompt_generation": None,
                "quality_validation": None,
                "current_step": "initializing",
                "error": None,
                "status": "Starting document processing workflow",
                "workflow_id": workflow_id,
                "final_result": None
            }

            # Compile and execute the workflow
            workflow = self.graph.compile()
            final_state = await workflow.ainvoke(initial_state)

            logger.info(f"Document processing workflow completed for {file_name}")
            return final_state.get('final_result', final_state)

        except Exception as e:
            logger.error(f"Workflow execution error for {file_name}: {str(e)}")
            return {
                'workflow_id': str(uuid.uuid4()),
                'document_info': {
                    'file_name': file_name,
                    'document_type': document_type,
                    'processed_at': datetime.now().isoformat()
                },
                'overall_status': 'error',
                'error': f"Workflow execution failed: {str(e)}",
                'failed_at_step': 'workflow_execution'
            }

    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status and capabilities"""
        return {
            'workflow_name': self.workflow_name,
            'status': 'active',
            'agents': [
                self.parsing_agent.get_agent_status(),
                self.content_agent.get_agent_status(),
                self.prompt_agent.get_agent_status(),
                self.qa_agent.get_agent_status()
            ],
            'processing_steps': [node.value for node in ProcessingNode],
            'version': '1.0.0'
        }


# Global instance
document_processing_workflow = DocumentProcessingWorkflow()
