"""
Content Analysis Agent for RFP/RFO document processing
Specialized in analyzing document content and extracting insights
"""
import logging
from typing import Dict, List, Any, Optional
from langchain.schema import BaseMessage, HumanMessage, SystemMessage
from services.azure_openai_service import azure_openai_service
from services.qdrant_service import qdrant_service

logger = logging.getLogger(__name__)


class ContentAnalysisAgent:
    """Agent specialized in content analysis and insight extraction"""
    
    def __init__(self):
        """Initialize the content analysis agent"""
        self.agent_name = "ContentAnalysisAgent"
        self.capabilities = [
            "content_summarization",
            "key_requirements_extraction",
            "compliance_analysis",
            "gap_identification",
            "content_classification"
        ]
        
        self.system_message = """You are a specialized Content Analysis Agent for RFP/RFO processing.
Your primary responsibilities are:

1. CONTENT ANALYSIS:
   - Analyze document content for key themes and topics
   - Extract critical requirements and specifications
   - Identify compliance requirements and standards
   - Summarize complex sections into digestible insights

2. REQUIREMENT EXTRACTION:
   - Identify mandatory vs. optional requirements
   - Extract technical specifications and constraints
   - Categorize requirements by type (functional, technical, legal, etc.)
   - Assess requirement clarity and completeness

3. COMPLIANCE ANALYSIS:
   - Identify regulatory and legal requirements
   - Check for standard compliance references
   - Flag potential compliance gaps or ambiguities
   - Assess risk factors

4. CONTENT QUALITY ASSESSMENT:
   - Evaluate content clarity and completeness
   - Identify missing information or ambiguities
   - Assess document organization and flow
   - Recommend content improvements

Provide detailed, structured analysis with clear categorization and actionable insights."""
        
        logger.info(f"Initialized {self.agent_name}")
    
    async def analyze_document_content(
        self,
        document_id: str,
        analysis_type: str = "comprehensive"
    ) -> Dict[str, Any]:
        """
        Analyze document content and extract insights
        
        Args:
            document_id: Document identifier
            analysis_type: Type of analysis (comprehensive, requirements, compliance)
            
        Returns:
            Content analysis results
        """
        try:
            logger.info(f"{self.agent_name}: Starting content analysis for {document_id}")
            
            # Get document structure and content
            doc_structure = qdrant_service.get_document_structure(document_id)
            
            if not doc_structure:
                return {'error': 'Document not found', 'agent': self.agent_name}
            
            # Combine all chunks to get full content
            full_content = ""
            for chunk in doc_structure.get('chunks', []):
                full_content += chunk.get('content', '') + "\n"
            
            # Perform different types of analysis based on request
            if analysis_type == "comprehensive":
                analysis_result = await self._comprehensive_analysis(full_content, doc_structure)
            elif analysis_type == "requirements":
                analysis_result = await self._requirements_analysis(full_content)
            elif analysis_type == "compliance":
                analysis_result = await self._compliance_analysis(full_content)
            else:
                analysis_result = await self._comprehensive_analysis(full_content, doc_structure)
            
            return {
                'agent': self.agent_name,
                'document_id': document_id,
                'analysis_type': analysis_type,
                'analysis_result': analysis_result,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"{self.agent_name}: Error analyzing content for {document_id}: {str(e)}")
            return {
                'agent': self.agent_name,
                'document_id': document_id,
                'status': 'error',
                'error': str(e)
            }
    
    async def _comprehensive_analysis(
        self,
        content: str,
        doc_structure: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Perform comprehensive content analysis
        
        Args:
            content: Full document content
            doc_structure: Document structure information
            
        Returns:
            Comprehensive analysis results
        """
        try:
            # Limit content for API call
            content_sample = content[:6000]
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Perform comprehensive analysis of this RFP/RFO document:

DOCUMENT METADATA:
- Type: {doc_structure.get('metadata', {}).get('document_type', 'Unknown')}
- Title: {doc_structure.get('metadata', {}).get('title', 'Unknown')}
- Sections: {len(doc_structure.get('chunks', []))}

CONTENT (first 6000 chars):
{content_sample}

Please provide:
1. EXECUTIVE SUMMARY (2-3 sentences)
2. KEY THEMES AND TOPICS
3. CRITICAL REQUIREMENTS (mandatory vs optional)
4. COMPLIANCE REQUIREMENTS
5. TIMELINE AND DEADLINES
6. EVALUATION CRITERIA
7. POTENTIAL RISKS OR CONCERNS
8. CONTENT QUALITY ASSESSMENT
9. RECOMMENDATIONS FOR IMPROVEMENT

Format as structured analysis with clear sections.""")
            ]
            
            ai_response = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.3
            )
            
            # Extract key requirements using vector search
            key_requirements = await self._extract_key_requirements(content)
            
            # Identify compliance elements
            compliance_elements = await self._identify_compliance_elements(content)
            
            return {
                'executive_summary': ai_response,
                'key_requirements': key_requirements,
                'compliance_elements': compliance_elements,
                'content_quality_score': await self._assess_content_quality(content),
                'analysis_timestamp': doc_structure.get('metadata', {}).get('upload_date')
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {str(e)}")
            return {'error': str(e)}
    
    async def _requirements_analysis(self, content: str) -> Dict[str, Any]:
        """
        Analyze document for requirements extraction
        
        Args:
            content: Document content
            
        Returns:
            Requirements analysis results
        """
        try:
            content_sample = content[:6000]
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Extract and analyze requirements from this document:

CONTENT:
{content_sample}

Please identify and categorize:
1. MANDATORY REQUIREMENTS (must-have)
2. OPTIONAL REQUIREMENTS (nice-to-have)
3. TECHNICAL SPECIFICATIONS
4. FUNCTIONAL REQUIREMENTS
5. PERFORMANCE REQUIREMENTS
6. SECURITY REQUIREMENTS
7. COMPLIANCE REQUIREMENTS
8. TIMELINE REQUIREMENTS

For each requirement, provide:
- Category
- Description
- Priority level
- Clarity assessment
- Potential implementation challenges""")
            ]
            
            ai_response = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.2
            )
            
            return {
                'requirements_analysis': ai_response,
                'analysis_type': 'requirements_focused'
            }
            
        except Exception as e:
            logger.error(f"Error in requirements analysis: {str(e)}")
            return {'error': str(e)}
    
    async def _compliance_analysis(self, content: str) -> Dict[str, Any]:
        """
        Analyze document for compliance requirements
        
        Args:
            content: Document content
            
        Returns:
            Compliance analysis results
        """
        try:
            content_sample = content[:6000]
            
            messages = [
                SystemMessage(content=self.system_message),
                HumanMessage(content=f"""Analyze compliance requirements in this document:

CONTENT:
{content_sample}

Please identify:
1. REGULATORY REQUIREMENTS (laws, regulations)
2. INDUSTRY STANDARDS (ISO, NIST, etc.)
3. SECURITY COMPLIANCE (data protection, cybersecurity)
4. LEGAL REQUIREMENTS (contracts, liability)
5. CERTIFICATION REQUIREMENTS
6. AUDIT REQUIREMENTS
7. REPORTING REQUIREMENTS
8. RISK FACTORS

For each compliance area:
- Specific requirements
- Applicable standards/regulations
- Compliance verification methods
- Potential compliance gaps""")
            ]
            
            ai_response = await azure_openai_service.chat_completion(
                messages=[{"role": msg.type, "content": msg.content} for msg in messages],
                temperature=0.2
            )
            
            return {
                'compliance_analysis': ai_response,
                'analysis_type': 'compliance_focused'
            }
            
        except Exception as e:
            logger.error(f"Error in compliance analysis: {str(e)}")
            return {'error': str(e)}
    
    async def _extract_key_requirements(self, content: str) -> List[Dict[str, Any]]:
        """Extract key requirements using pattern matching and AI"""
        try:
            # Common requirement indicators
            requirement_patterns = [
                r'must\s+(?:be|have|provide|support|include)',
                r'shall\s+(?:be|have|provide|support|include)',
                r'required\s+to',
                r'mandatory',
                r'essential',
                r'critical'
            ]
            
            import re
            requirements = []
            
            for pattern in requirement_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    # Extract surrounding context
                    start = max(0, match.start() - 100)
                    end = min(len(content), match.end() + 200)
                    context = content[start:end].strip()
                    
                    requirements.append({
                        'pattern': pattern,
                        'context': context,
                        'position': match.start()
                    })
            
            return requirements[:20]  # Limit to top 20
            
        except Exception as e:
            logger.error(f"Error extracting key requirements: {str(e)}")
            return []
    
    async def _identify_compliance_elements(self, content: str) -> List[Dict[str, Any]]:
        """Identify compliance-related elements in the document"""
        try:
            compliance_keywords = [
                'compliance', 'regulation', 'standard', 'certification',
                'audit', 'security', 'privacy', 'gdpr', 'hipaa',
                'iso', 'nist', 'sox', 'pci', 'legal', 'liability'
            ]
            
            compliance_elements = []
            
            for keyword in compliance_keywords:
                if keyword.lower() in content.lower():
                    # Find contexts where this keyword appears
                    import re
                    pattern = rf'\b{re.escape(keyword)}\b'
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    
                    for match in matches:
                        start = max(0, match.start() - 150)
                        end = min(len(content), match.end() + 150)
                        context = content[start:end].strip()
                        
                        compliance_elements.append({
                            'keyword': keyword,
                            'context': context,
                            'position': match.start()
                        })
            
            return compliance_elements[:15]  # Limit results
            
        except Exception as e:
            logger.error(f"Error identifying compliance elements: {str(e)}")
            return []
    
    async def _assess_content_quality(self, content: str) -> float:
        """Assess the quality of document content"""
        try:
            # Basic quality metrics
            word_count = len(content.split())
            sentence_count = content.count('.') + content.count('!') + content.count('?')
            paragraph_count = content.count('\n\n') + 1
            
            # Calculate basic scores
            length_score = min(1.0, word_count / 5000)  # Normalize to 5000 words
            structure_score = min(1.0, paragraph_count / 50)  # Normalize to 50 paragraphs
            readability_score = min(1.0, sentence_count / word_count * 20) if word_count > 0 else 0
            
            # Average the scores
            overall_score = (length_score + structure_score + readability_score) / 3
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"Error assessing content quality: {str(e)}")
            return 0.0
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and capabilities"""
        return {
            'agent_name': self.agent_name,
            'status': 'active',
            'capabilities': self.capabilities,
            'version': '1.0.0'
        }
