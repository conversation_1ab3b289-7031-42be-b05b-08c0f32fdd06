import logging
import os
import shutil
import sys
import time

from dotenv import load_dotenv

from core import LLMClient
from core.FileWorker import create_worker
from core.Util import remove_markdown_warp

# Ensure UTF-8 encoding for stdout on Windows
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stderr)],
)
logger = logging.getLogger(__name__)

load_dotenv()


def completion(
    message,
    model="",
    system_prompt="",
    image_paths=None,
    temperature=0.5,
    max_tokens=8192,
    retry_times=3,
):
    """
    Call Azure OpenAI's completion interface for text generation

    Args:
        message (str): User input message
        model (str): Model deployment name
        system_prompt (str, optional): System prompt, defaults to empty string
        image_paths (List[str], optional): List of image paths, defaults to None
        temperature (float, optional): Temperature for text generation, defaults to 0.5
        max_tokens (int, optional): Maximum number of tokens for generated text, defaults to 8192
    Returns:
        str: Generated text content
    """

    # Get Azure OpenAI configuration from environment variables
    api_key = os.getenv("AZURE_OPENAI_API_KEY")
    if not api_key:
        logger.error("Please set the AZURE_OPENAI_API_KEY environment variable")
        exit(1)
    
    azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    if not azure_endpoint:
        logger.error("Please set the AZURE_OPENAI_ENDPOINT environment variable")
        exit(1)
    
    api_version = os.getenv("AZURE_OPENAI_API_VERSION")
    if not api_version:
        api_version = "2024-02-01"  # Default to latest stable version

    # If no model is specified, use the default model deployment
    if not model:
        model = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        if not model:
            model = "gpt-4o"  # Default deployment name

    # Initialize LLMClient with Azure OpenAI configuration
    client = LLMClient.LLMClient(
        azure_endpoint=azure_endpoint, 
        api_key=api_key, 
        model=model,
        api_version=api_version
    )
    # Call completion method with retry mechanism
    last_error = None
    for attempt in range(retry_times):
        try:
            logger.info(f"Attempt {attempt + 1}/{retry_times} for API call")
            response = client.completion(
                user_message=message,
                system_prompt=system_prompt,
                image_paths=image_paths,
                temperature=temperature,
                max_tokens=max_tokens,
            )
            if response:
                logger.info(f"✅ API call successful on attempt {attempt + 1}")
                return response
            else:
                logger.warning(f"Empty response on attempt {attempt + 1}")
                last_error = "Empty response from API"
        except Exception as e:
            last_error = str(e)
            logger.error(f"❌ Attempt {attempt + 1}/{retry_times} failed: {last_error}")
            if attempt < retry_times - 1:  # Don't sleep on last attempt
                logger.info(f"Waiting 2 seconds before retry...")
                time.sleep(2)
    
    logger.error(f"All {retry_times} attempts failed. Last error: {last_error}")
    return ""


def convert_image_to_markdown(image_path):
    """
    Convert image to Markdown format
    Args:
        image_path (str): Path to the image
    Returns:
        str: Converted Markdown string
    """
    logger.info(f"🖼️  Starting conversion of image: {os.path.basename(image_path)}")
    
    system_prompt = """
You are a helpful assistant that can convert images to Markdown format. You are given an image, and you need to convert it to Markdown format. Please output the Markdown content only, without any other text.
"""
    user_prompt = """
Below is the image of one page of a document, please read the content in the image and transcribe it into plain Markdown format. Please note:
1. Identify heading levels, text styles, formulas, and the format of table rows and columns
2. Mathematical formulas should be transcribed using LaTeX syntax, ensuring consistency with the original
3. Please output the Markdown content only, without any other text.

Output Example:
```markdown
{example}
```
"""

    try:
        response = completion(
            message=user_prompt,
            system_prompt=system_prompt,
            image_paths=[image_path],
            temperature=0.3,
            max_tokens=8192,
        )
        
        if not response:
            logger.error(f"❌ No response received for image: {os.path.basename(image_path)}")
            return "# Error\n\nFailed to convert image to markdown."
        
        response = remove_markdown_warp(response, "markdown")
        logger.info(f"✅ Successfully converted image: {os.path.basename(image_path)}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to convert image {os.path.basename(image_path)}: {str(e)}")
        return f"# Error\n\nFailed to convert image: {str(e)}"


def list_output_directories():
    """
    List available output directories that can be resumed
    """
    output_base = "output"
    if not os.path.exists(output_base):
        return []
    
    dirs = []
    for item in os.listdir(output_base):
        item_path = os.path.join(output_base, item)
        if os.path.isdir(item_path):
            # Check if it has input file and some image files
            has_input = any(
                os.path.exists(os.path.join(item_path, f"input{ext}"))
                for ext in ['.pdf', '.jpg', '.jpeg', '.png', '.bmp']
            )
            if has_input:
                # Count existing markdown files
                md_count = len([f for f in os.listdir(item_path) if f.endswith('.md')])
                dirs.append((item_path, md_count))
    
    return sorted(dirs, key=lambda x: x[0], reverse=True)


def print_resume_help():
    """
    Print help information about resumable output directories
    """
    dirs = list_output_directories()
    if dirs:
        print("Available output directories to resume from:")
        for dir_path, md_count in dirs:
            print(f"  {dir_path} ({md_count} markdown files)")
        print("\nUsage: python main.py --resume <directory_path>")
    else:
        print("No resumable output directories found.")


def generate_output_filename(input_path=None, resume_dir=None):
    """
    Generate a meaningful output filename based on input
    Always returns a filename (not a path) to be placed in current working directory
    """
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    
    if input_path:
        # Extract base name from input file
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        if base_name and base_name != "input":
            return f"{base_name}_converted_{timestamp}.md"
    
    if resume_dir:
        # Use directory name as base
        dir_name = os.path.basename(resume_dir.rstrip('/\\'))
        return f"resumed_{dir_name}.md"
    
    # Fallback to timestamp
    return f"markpdfdown_output_{timestamp}.md"


def check_azure_openai_config():
    """
    Check if Azure OpenAI configuration is properly set
    """
    logger.info("🔧 Checking Azure OpenAI configuration...")
    
    required_vars = {
        "AZURE_OPENAI_API_KEY": os.getenv("AZURE_OPENAI_API_KEY"),
        "AZURE_OPENAI_ENDPOINT": os.getenv("AZURE_OPENAI_ENDPOINT"),
    }
    
    missing_vars = []
    for var_name, var_value in required_vars.items():
        if not var_value:
            missing_vars.append(var_name)
        else:
            logger.info(f"✅ {var_name}: {'*' * min(10, len(var_value))}...")
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set the required environment variables or create a .env file")
        logger.error("See .env.example for reference")
        exit(1)
    
    # Optional variables with defaults
    api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-01")
    deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4o")
    
    logger.info(f"✅ API Version: {api_version}")
    logger.info(f"✅ Deployment Name: {deployment_name}")
    logger.info("✅ Azure OpenAI configuration looks good!")
    
    return True


def test_azure_openai_connection():
    """
    Test Azure OpenAI connection with a simple request
    """
    logger.info("🔍 Testing Azure OpenAI connection...")
    
    try:
        # Simple test call
        test_response = completion(
            message="Hello, please respond with 'Connection successful'",
            system_prompt="You are a helpful assistant. Respond exactly as requested.",
            temperature=0.0,
            max_tokens=50,
            retry_times=1
        )
        
        if test_response and "successful" in test_response.lower():
            logger.info("✅ Azure OpenAI connection test successful!")
            return True
        else:
            logger.warning(f"⚠️  Azure OpenAI connection test returned unexpected response: {test_response}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Azure OpenAI connection test failed: {str(e)}")
        return False


if __name__ == "__main__":
    import argparse
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description="Convert PDF/images to Markdown using Azure OpenAI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py < input.pdf > output.md
  python main.py 5 < input.pdf > output.md          # Process only first 5 pages
  python main.py 3 10 < input.pdf > output.md       # Process pages 3-10
  python main.py --list-dirs                        # List available directories to resume
  python main.py --resume output/20250627144306/    # Resume from existing output
  python main.py --output final.md < input.pdf      # Save to file instead of stdout
  python main.py --save-and-cleanup < input.pdf     # Save final output, clean intermediate files
  python main.py --resume output/20250627144306/ --save-and-cleanup  # Resume, save, clean intermediate
        """
    )
    parser.add_argument('start_page', nargs='?', type=int, default=1,
                       help='Start page number (default: 1)')
    parser.add_argument('end_page', nargs='?', type=int, default=0,
                       help='End page number (default: all pages)')
    parser.add_argument('--resume', type=str, metavar='OUTPUT_DIR',
                       help='Resume processing from existing output directory')
    parser.add_argument('--no-cleanup', action='store_true',
                       help='Keep output directory after processing (useful for debugging)')
    parser.add_argument('--list-dirs', action='store_true',
                       help='List available output directories that can be resumed')
    parser.add_argument('--output', '-o', type=str, metavar='OUTPUT_FILE',
                       help='Save combined markdown to file (default: stdout only)')
    parser.add_argument('--save-and-cleanup', action='store_true',
                       help='Save combined markdown to file and clean up intermediate files (preserves final output)')
    
    args = parser.parse_args()
    
    # Handle --list-dirs option
    if args.list_dirs:
        print_resume_help()
        exit(0)
    
    # Check Azure OpenAI configuration before processing
    check_azure_openai_config()
    
    # Test Azure OpenAI connection
    if not test_azure_openai_connection():
        logger.error("❌ Azure OpenAI connection failed. Please check your configuration.")
        logger.error("💡 Make sure your Azure OpenAI endpoint and API key are correct.")
        exit(1)
    
    start_page = args.start_page
    end_page = args.end_page
    resume_dir = args.resume
    keep_files = args.no_cleanup
    output_file = args.output
    save_and_cleanup = args.save_and_cleanup

    # If save_and_cleanup is used, we need an output file
    if save_and_cleanup and not output_file:
        # Generate a meaningful output filename
        if resume_dir:
            output_file = generate_output_filename(resume_dir=resume_dir)
        else:
            output_file = generate_output_filename()
        logger.info(f"Using generated output filename: {output_file}")
    
    # Override keep_files if save_and_cleanup is specified
    if save_and_cleanup:
        keep_files = False  # We'll clean up after saving

    if resume_dir:
        # Resume from existing output directory
        if not os.path.exists(resume_dir):
            logger.error(f"Resume directory does not exist: {resume_dir}")
            exit(1)
        
        output_dir = resume_dir.rstrip('/')  # Remove trailing slash if present
        logger.info(f"Resuming processing from directory: {output_dir}")
        
        # Find the input file in the resume directory
        input_files = []
        for ext in ['.pdf', '.jpg', '.jpeg', '.png', '.bmp']:
            input_pattern = os.path.join(output_dir, f"input{ext}")
            if os.path.exists(input_pattern):
                input_files.append(input_pattern)
        
        if not input_files:
            logger.error(f"No input file found in resume directory: {output_dir}")
            exit(1)
        
        input_path = input_files[0]
        logger.info(f"Found input file: {input_path}")
        
    else:
        # Original behavior: create new output directory and read from stdin
        # Read binary data from standard input
        input_data = sys.stdin.buffer.read()
        if not input_data:
            logger.error("No input data received")
            logger.error(
                "Usage: python main.py [start_page] [end_page] < path_to_input.pdf"
            )
            exit(1)

        # Create output directory
        output_dir = f"output/{time.strftime('%Y%m%d%H%M%S')}"
        os.makedirs(output_dir, exist_ok=True)

        # Try to get extension from file name
        input_filename = os.path.basename(sys.stdin.buffer.name)
        input_ext = os.path.splitext(input_filename)[1]

        # If there is no extension or the file comes from standard input, try to determine the type by file content
        if not input_ext or input_filename == "<stdin>":
            # PDF file magic number/signature is %PDF-
            if input_data.startswith(b"%PDF-"):
                input_ext = ".pdf"
                logger.info("Recognized as PDF file by file content")
            # JPEG file magic number/signature is FF D8 FF DB
            elif input_data.startswith(b"\xff\xd8\xff\xdb"):
                input_ext = ".jpeg"
                logger.info("Recognized as JPEG file by file content")
            # JPG file magic number/signature is FF D8 FF E0
            elif input_data.startswith(b"\xff\xd8\xff\xe0"):
                input_ext = ".jpg"
                logger.info("Recognized as JPG file by file content")
            # PNG file magic number/signature is 89 50 4E 47
            elif input_data.startswith(b"\x89\x50\x4e\x47"):
                input_ext = ".png"
                logger.info("Recognized as PNG file by file content")
            # BMP file magic number/signature is 42 4D
            elif input_data.startswith(b"\x42\x4d"):
                input_ext = ".bmp"
                logger.info("Recognized as BMP file by file content")
            else:
                logger.error("Unsupported file type")
                exit(1)

        input_path = os.path.join(output_dir, f"input{input_ext}")
        with open(input_path, "wb") as f:
            f.write(input_data)

    # create file worker
    try:
        worker = create_worker(input_path, start_page, end_page)
    except ValueError as e:
        logger.error(str(e))
        exit(1)

    # convert to images
    img_paths = worker.convert_to_images()
    logger.info("Image conversion completed")

    # Check for existing markdown files to resume processing
    existing_md_files = set()
    if resume_dir:
        for md_file in os.listdir(output_dir):
            if md_file.endswith('.md'):
                # Extract the base image name from the markdown filename
                # e.g., "page_001.jpg.md" -> "page_001.jpg"
                img_name = md_file[:-3]  # Remove .md extension
                existing_md_files.add(img_name)
        
        if existing_md_files:
            logger.info(f"Found {len(existing_md_files)} existing markdown files, skipping those images")

    # convert to markdown
    markdown = ""
    processed_count = 0
    skipped_count = 0
    total_images = len(img_paths)
    
    logger.info(f"🚀 Starting markdown conversion for {total_images} images")
    logger.info(f"📊 Processing plan: {total_images - len(existing_md_files)} new images, {len(existing_md_files)} existing to skip")
    
    for i, img_path in enumerate(sorted(img_paths), 1):
        img_path = img_path.replace("\\", "/")
        img_basename = os.path.basename(img_path)
        md_file_path = os.path.join(output_dir, f"{img_basename}.md")
        
        logger.info(f"📄 Processing [{i}/{total_images}] {img_basename}")
        
        # Check if markdown file already exists
        if img_basename in existing_md_files:
            logger.info(f"⏭️  Skipping {img_path} - markdown already exists")
            skipped_count += 1
            # Read existing content and add to final markdown
            try:
                with open(md_file_path, "r", encoding="utf-8") as f:
                    existing_content = f.read()
                markdown += existing_content
                markdown += "\n\n"
            except Exception as e:
                logger.warning(f"Could not read existing markdown file {md_file_path}: {e}")
            continue
        
        logger.info(f"🔄 Converting image {img_path} to Markdown")
        try:
            content = convert_image_to_markdown(img_path)
            if content:
                # Write to file with UTF-8 encoding to handle Unicode characters
                with open(md_file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                markdown += content
                markdown += "\n\n"
                processed_count += 1
                logger.info(f"✅ [{i}/{total_images}] Successfully processed {img_basename}")
            else:
                logger.error(f"❌ [{i}/{total_images}] No content generated for {img_basename}")
        except Exception as e:
            logger.error(f"❌ [{i}/{total_images}] Failed to process {img_basename}: {str(e)}")
            # Continue with next image instead of stopping
            continue

    # Log processing summary
    total_images = len(img_paths)
    logger.info(f"Processing completed: {processed_count} new, {skipped_count} skipped, {total_images} total images")

    # Save combined markdown to file if specified
    final_output_path = None
    if output_file or save_and_cleanup:
        if not output_file:
            # Generate filename based on context - always just the filename, not full path
            filename = None
            if resume_dir:
                filename = generate_output_filename(resume_dir=resume_dir)
            else:
                filename = generate_output_filename(input_path=input_path)
            # Always place generated files in current working directory
            final_output_path = os.path.join(os.getcwd(), filename)
        else:
            # User specified output file
            final_output_path = output_file
            # If user specified a relative path, make it absolute from current directory
            if not os.path.isabs(final_output_path):
                final_output_path = os.path.join(os.getcwd(), final_output_path)
        
        # Double-check: ensure the final output file is never inside any output directory
        # This is critical for resume operations to prevent deleting the final output
        output_dir_abs = os.path.abspath(output_dir)
        final_output_abs = os.path.abspath(final_output_path)
        
        if final_output_abs.startswith(output_dir_abs + os.sep) or final_output_abs == output_dir_abs:
            # File would be inside output directory - move it to current directory
            safe_filename = os.path.basename(final_output_path)
            final_output_path = os.path.join(os.getcwd(), safe_filename)
            logger.info(f"Relocated output file to prevent deletion: {final_output_path}")
            
        try:
            with open(final_output_path, "w", encoding="utf-8") as f:
                f.write(markdown)
            logger.info(f"Combined markdown saved to: {final_output_path}")
            
            # Also output to stdout unless we're doing save_and_cleanup
            if not save_and_cleanup:
                print(markdown)
        except Exception as e:
            logger.error(f"Failed to save markdown to file {final_output_path}: {e}")
            # Fallback to stdout
            print(markdown)
            final_output_path = None  # Mark as failed so we don't reference it later
    else:
        # Default behavior: output to stdout only
        print(markdown)

    logger.info("Image conversion to Markdown completed")
    
    # Handle cleanup based on options
    if save_and_cleanup:
        # Clean up intermediate files but preserve the final combined markdown
        cleanup_path = os.path.abspath(output_dir)
        try:
            if final_output_path and os.path.exists(final_output_path):
                final_abs = os.path.abspath(final_output_path)
                logger.info(f"✅ PRESERVED: Final combined markdown → {final_abs}")
                
                # Double-check that we're not about to delete the final output
                if final_abs.startswith(cleanup_path + os.sep) or final_abs == cleanup_path:
                    logger.error(f"❌ CRITICAL: Final output is inside cleanup directory!")
                    logger.error(f"   Final output: {final_abs}")
                    logger.error(f"   Cleanup dir:  {cleanup_path}")
                    logger.error(f"   Aborting cleanup to prevent data loss!")
                    exit(1)
            
            shutil.rmtree(output_dir)
            logger.info(f"🧹 CLEANED UP: All intermediate files from → {cleanup_path}")
            
            if resume_dir:
                logger.info(f"📂 RESUME: Successfully resumed from {resume_dir} and cleaned up")
            
        except Exception as e:
            logger.warning(f"Failed to clean up directory {output_dir}: {e}")
    elif not keep_files:
        # Original cleanup behavior - remove everything
        try:
            shutil.rmtree(output_dir)
            logger.info(f"🧹 CLEANED UP: Output directory → {output_dir}")
        except Exception as e:
            logger.warning(f"Failed to clean up directory {output_dir}: {e}")
    else:
        # Preserve everything for debugging/resume
        logger.info(f"📁 PRESERVED: Output files in → {output_dir}")
        if final_output_path and os.path.exists(final_output_path):
            logger.info(f"📄 PRESERVED: Combined markdown → {final_output_path}")
    
    exit(0)
