[project]
name = "markpdfdown"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "openai>=1.66.3",
    "pymupdf==1.25.3",
    "pypdf2==3.0.1",
    "python-dotenv>=1.1.0",
]

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
    "pytest==8.3.5",
    "ruff>=0.11.11",
]

# Ruff 配置
[tool.ruff]
line-length = 88
target-version = "py313"

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "UP007", # Use `X | Y` for type annotations
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"

[tool.ruff.lint.isort]
known-first-party = ["markpdfdown"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["markpdfdown"]

[tool.hatch.build.targets.wheel.force-include]
"." = "."
