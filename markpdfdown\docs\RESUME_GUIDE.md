# Resume Functionality Usage Guide

## Overview

The MarkPDFDown tool now supports resuming interrupted processing sessions. This is particularly useful for large PDF files that might take a long time to process or when processing gets interrupted.

## How It Works

When processing a PDF or image file, the tool:

1. **Creates an output directory** with timestamp (e.g., `output/20250627144306/`)
2. **Saves the input file** in that directory
3. **Converts to individual page images** 
4. **Processes each image to markdown** and saves as separate `.md` files
5. **Combines all markdown** for final output
6. **Cleans up the directory** (unless `--no-cleanup` is used)

## Resume Process

When using `--resume`:

1. **Checks the specified directory** for existing `.md` files
2. **Skips processing** for images that already have corresponding markdown files
3. **Processes only missing pages**
4. **Combines all content** (existing + new) in the final output

## Command Line Options

### Basic Usage
```bash
# Normal processing (creates new output directory)
python main.py < input.pdf > output.md

# Keep output files for later resume
python main.py --no-cleanup < input.pdf > output.md
```

### Resume Commands
```bash
# List available directories to resume from
python main.py --list-dirs

# Resume from specific directory
python main.py --resume output/20250627144306/

# Resume and keep files afterward
python main.py --resume output/20250627144306/ --no-cleanup

# Save to specific file
python main.py --output my_document.md < input.pdf

# Save to file and clean up everything
python main.py --save-and-cleanup < input.pdf

# Resume, save, and clean up
python main.py --resume output/20250627144306/ --save-and-cleanup
```

### Save and Cleanup Options
```bash
# Save combined output to file (also outputs to stdout)  
python main.py --output final_document.md < input.pdf

# Save to auto-generated filename and clean everything
python main.py --save-and-cleanup < input.pdf

# Resume with save and cleanup
python main.py --resume output/20250627144306/ --save-and-cleanup
```

### Page Range with Resume
```bash
# Process specific pages and keep output
python main.py 1 10 --no-cleanup < input.pdf > output.md

# Resume processing remaining pages
python main.py --resume output/20250627144306/
```

## Use Cases

### 1. Large PDF Processing
```bash
# Start processing a large PDF
python main.py --no-cleanup < large_document.pdf > output.md

# If it gets interrupted, resume from where it left off
python main.py --list-dirs  # Find the output directory
python main.py --resume output/20250627144306/
```

### 2. Debugging Specific Pages
```bash
# Process first 5 pages to test
python main.py 1 5 --no-cleanup < document.pdf > test_output.md

# If results look good, continue from page 6 onward
python main.py --resume output/20250627144306/
```

### 3. Batch Processing with Error Recovery
```bash
# Process with error handling
python main.py --no-cleanup < document.pdf > output.md || echo "Processing interrupted"

# Resume processing
python main.py --resume output/20250627144306/ > complete_output.md
```

### 4. Complete Processing with Auto-Cleanup
```bash
# Process and automatically save to file with cleanup
python main.py --save-and-cleanup < document.pdf
# Creates: markpdfdown_output_20250627_144306.md (in current directory)

# Resume and save with cleanup (SAFE - final output preserved outside resume folder)
python main.py --resume output/20250627144306/ --save-and-cleanup
# Creates: resumed_20250627144306.md (in current directory, NOT in resume folder)

# Custom filename with save and cleanup
python main.py --save-and-cleanup --output final_report.md < document.pdf
# Creates: final_report.md (in current directory)
```

**🔒 Safety for Resume Operations:**
- Final combined markdown is ALWAYS saved outside the resume directory
- This prevents accidental deletion when cleaning up the resume directory
- The tool validates file locations before cleanup to prevent data loss

## Directory Structure

When processing, the tool creates this structure:

```
output/
└── 20250627144306/          # Timestamp directory
    ├── input.pdf            # Original input file
    ├── page_001.jpg         # Converted page images
    ├── page_002.jpg
    ├── page_003.jpg
    ├── page_001.jpg.md      # Markdown for each page
    ├── page_002.jpg.md
    └── page_003.jpg.md
```

## Tips and Best Practices

### 1. Always Use --no-cleanup for Large Files
```bash
# For large or important files, keep the intermediate files
python main.py --no-cleanup < important_document.pdf > output.md
```

### 2. Check Progress Before Resuming
```bash
# See what directories are available and how many pages are done
python main.py --list-dirs
```

### 3. Resume with Confidence
The resume feature is safe - it only processes missing pages and preserves existing work.

### 4. Combine with Page Ranges
```bash
# Process document in chunks
python main.py 1 20 --no-cleanup < document.pdf > part1.md
python main.py 21 40 --no-cleanup < document.pdf > part2.md
# etc.
```

## Troubleshooting

### "Resume directory does not exist"
- Check the exact path you're providing
- Use `--list-dirs` to see available directories
- Ensure you include the full path: `output/20250627144306/`

### "No input file found in resume directory"
- The directory may not be a valid MarkPDFDown output directory
- Check that it contains an `input.pdf` or similar file

### Missing Pages in Output
- The tool only processes pages that don't have corresponding `.md` files
- Check the directory to see which pages were already processed
- Existing `.md` files are included in the final output

## Examples

### Complete Workflow Example
```bash
# 1. Start processing a large document
python main.py --no-cleanup < large_report.pdf > output.md

# 2. Process gets interrupted at page 15 of 50

# 3. Check available resume directories
python main.py --list-dirs
# Output: output/20250627144306/ (15 markdown files)

# 4. Resume processing
python main.py --resume output/20250627144306/ > complete_output.md

# 5. Now you have the complete document processed
```

### Error Recovery Example
```bash
# Processing fails due to network issues
python main.py --no-cleanup < document.pdf > output.md
# Error: Azure OpenAI API request failed after page 8

# Fix network/API issues, then resume
python main.py --resume output/20250627144306/ > recovered_output.md
```

The resume functionality makes MarkPDFDown much more robust for production use and large document processing.
