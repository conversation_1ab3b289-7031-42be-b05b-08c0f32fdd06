"""
Demo Script for RFP/RFO Document Processing System
Demonstrates the complete workflow with sample data
"""
import asyncio
import tempfile
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from workflows.document_processing_workflow import document_processing_workflow
from services.azure_openai_service import azure_openai_service
from services.qdrant_service import qdrant_service
from services.document_processor import document_processor


class DocumentProcessingDemo:
    """Demo class for showcasing the document processing system"""
    
    def __init__(self):
        """Initialize the demo"""
        self.demo_name = "RFP/RFO Document Processing System Demo"
        print(f"🚀 {self.demo_name}")
        print("=" * 60)
    
    def create_sample_rfp_document(self) -> tuple:
        """Create a comprehensive sample RFP document for demonstration"""
        
        sample_content = """# REQUEST FOR PROPOSAL
Software Development Services for Enterprise Web Application

## 1. EXECUTIVE SUMMARY

Our organization is seeking qualified vendors to develop a comprehensive web-based application that will modernize our business operations and improve customer engagement. This RFP outlines our requirements for a full-stack development project with an estimated timeline of 8 months.

## 2. PROJECT OVERVIEW

### 2.1 Background
We are a mid-size enterprise looking to replace our legacy systems with a modern, scalable web application. The current system is outdated and no longer meets our growing business needs.

### 2.2 Objectives
- Modernize our technology infrastructure
- Improve operational efficiency by 40%
- Enhance customer experience and engagement
- Reduce manual processes and data entry
- Implement real-time reporting and analytics

## 3. TECHNICAL REQUIREMENTS

### 3.1 Core Technology Stack
The solution MUST include:
- Backend: Python (Django/FastAPI) or Node.js
- Frontend: React.js or Vue.js with responsive design
- Database: PostgreSQL or MySQL with proper indexing
- API: RESTful services with OpenAPI documentation
- Authentication: OAuth 2.0 and JWT implementation

### 3.2 Infrastructure Requirements
- Cloud deployment (AWS, Azure, or GCP)
- Container orchestration (Docker/Kubernetes)
- CI/CD pipeline implementation
- Automated testing framework
- Monitoring and logging solutions

### 3.3 Performance Requirements
- Page load times under 2 seconds
- Support for 1000+ concurrent users
- 99.9% uptime availability
- Mobile-responsive design
- Cross-browser compatibility

## 4. FUNCTIONAL REQUIREMENTS

### 4.1 User Management
- Multi-role user authentication system
- User profile management
- Permission-based access control
- Password reset and security features

### 4.2 Core Business Features
- Dashboard with real-time analytics
- Document management system
- Workflow automation capabilities
- Reporting and export functionality
- Integration with existing systems

### 4.3 Administrative Features
- System configuration management
- User activity monitoring
- Backup and recovery procedures
- Data import/export capabilities

## 5. COMPLIANCE AND SECURITY REQUIREMENTS

### 5.1 Security Standards
- GDPR compliance REQUIRED
- SOC 2 Type II certification preferred
- Data encryption at rest and in transit
- Regular security audits and penetration testing
- Secure coding practices implementation

### 5.2 Accessibility Requirements
- WCAG 2.1 AA compliance MANDATORY
- Section 508 compliance required
- Screen reader compatibility
- Keyboard navigation support

### 5.3 Data Protection
- Personal data anonymization capabilities
- Right to be forgotten implementation
- Data retention policy compliance
- Audit trail for all data modifications

## 6. PROJECT TIMELINE AND MILESTONES

### 6.1 Project Phases
- Phase 1: Requirements Analysis and Design (6 weeks)
- Phase 2: Backend Development (12 weeks)
- Phase 3: Frontend Development (10 weeks)
- Phase 4: Integration and Testing (6 weeks)
- Phase 5: Deployment and Training (4 weeks)

### 6.2 Key Deliverables
- Technical architecture documentation
- Database design and implementation
- API documentation and testing
- User interface mockups and implementation
- Comprehensive testing reports
- Deployment and maintenance documentation

## 7. EVALUATION CRITERIA

### 7.1 Technical Capability (40%)
- Demonstrated expertise in required technologies
- Quality of proposed technical architecture
- Development methodology and best practices
- Code quality and documentation standards

### 7.2 Experience and References (25%)
- Relevant project portfolio
- Client testimonials and references
- Team qualifications and certifications
- Previous experience with similar projects

### 7.3 Project Management (20%)
- Realistic project timeline and milestones
- Risk management and mitigation strategies
- Communication and reporting procedures
- Quality assurance processes

### 7.4 Cost and Value (15%)
- Total project cost breakdown
- Value for money assessment
- Payment terms and conditions
- Ongoing maintenance and support costs

## 8. SUBMISSION REQUIREMENTS

### 8.1 Proposal Components
Your proposal MUST include:
- Executive summary (2 pages maximum)
- Technical approach and architecture
- Detailed project timeline with milestones
- Team composition and qualifications
- Cost breakdown and payment schedule
- References from similar projects

### 8.2 Submission Guidelines
- Proposal deadline: 45 days from RFP issuance
- Format: PDF document, maximum 75 pages
- Submission method: <NAME_EMAIL>
- Late submissions will NOT be considered

### 8.3 Evaluation Process
- Initial screening: 1 week
- Technical evaluation: 2 weeks
- Vendor presentations: 1 week
- Final selection: 1 week
- Contract negotiation: 2 weeks

## 9. TERMS AND CONDITIONS

### 9.1 Contract Terms
- Contract type: Fixed-price with milestone payments
- Payment schedule: 20% upfront, 60% milestone-based, 20% completion
- Intellectual property rights transfer upon final payment
- 12-month warranty period for all deliverables

### 9.2 Legal Requirements
- Valid business license and insurance required
- Background checks for all team members
- Non-disclosure agreement execution
- Compliance with local labor laws

### 9.3 Support and Maintenance
- 90-day post-deployment support included
- Optional ongoing maintenance contract available
- Knowledge transfer and training requirements
- Documentation and source code delivery

## 10. CONTACT INFORMATION

### 10.1 Primary Contact
John Smith, Procurement Manager
Email: <EMAIL>
Phone: (*************

### 10.2 Technical Contact
Sarah Johnson, CTO
Email: <EMAIL>
Phone: (*************

### 10.3 Questions and Clarifications
- All questions must be submitted in writing
- Response deadline: 5 business days
- Answers will be shared with all vendors
- No verbal commitments will be honored

---

This RFP represents a significant opportunity for qualified vendors to partner with our organization in modernizing our technology infrastructure. We look forward to receiving innovative and competitive proposals that demonstrate clear value and technical excellence.
"""
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as tmp_file:
            tmp_file.write(sample_content)
            tmp_file_path = tmp_file.name
        
        return tmp_file_path, sample_content
    
    async def run_demo(self):
        """Run the complete demo workflow"""
        try:
            print("📄 Creating sample RFP document...")
            file_path, content = self.create_sample_rfp_document()
            
            print(f"✅ Sample document created: {len(content)} characters")
            print(f"📁 Temporary file: {file_path}")
            
            print("\n🔄 Starting document processing workflow...")
            print("This may take a few minutes...")
            
            # Execute the complete workflow
            result = await document_processing_workflow.execute(
                file_path=file_path,
                file_name="sample_rfp_demo.txt",
                document_type="RFP"
            )
            
            # Display results
            self.display_results(result)
            
            # Cleanup
            os.unlink(file_path)
            print(f"\n🧹 Cleaned up temporary file: {file_path}")
            
        except Exception as e:
            print(f"❌ Demo failed with error: {str(e)}")
            raise
    
    def display_results(self, result):
        """Display the processing results in a formatted way"""
        print("\n" + "="*60)
        print("📊 PROCESSING RESULTS")
        print("="*60)
        
        # Overall status
        status = result.get('overall_status', 'unknown')
        status_emoji = "✅" if status == 'success' else "❌"
        print(f"\n{status_emoji} Overall Status: {status.upper()}")
        
        # Document information
        doc_info = result.get('document_info', {})
        print(f"\n📄 Document Information:")
        print(f"   • File Name: {doc_info.get('file_name', 'Unknown')}")
        print(f"   • Document Type: {doc_info.get('document_type', 'Unknown')}")
        print(f"   • Processed At: {doc_info.get('processed_at', 'Unknown')}")
        
        # Processing summary
        summary = result.get('processing_summary', {})
        if summary:
            print(f"\n📈 Processing Summary:")
            print(f"   • Document Parsed: {'✅' if summary.get('document_parsed') else '❌'}")
            print(f"   • Sections Found: {summary.get('sections_found', 0)}")
            print(f"   • Headings Found: {summary.get('headings_found', 0)}")
            print(f"   • Content Analyzed: {'✅' if summary.get('content_analyzed') else '❌'}")
            print(f"   • Prompts Generated: {summary.get('prompts_generated', 0)}")
            print(f"   • Quality Score: {summary.get('quality_score', 0):.2f}")
            print(f"   • Stored in Database: {'✅' if summary.get('stored_in_database') else '❌'}")
        
        # Parsing results
        parsing_result = result.get('parsing_result', {})
        if parsing_result and parsing_result.get('parsing_status') == 'success':
            structure = parsing_result.get('structure', {})
            print(f"\n🔍 Document Structure Analysis:")
            print(f"   • Document Type: {structure.get('document_type', 'Unknown')}")
            
            headings = structure.get('headings', [])
            if headings:
                print(f"   • Key Headings Found:")
                for heading in headings[:5]:  # Show first 5 headings
                    level = "  " * heading.get('level', 1)
                    print(f"     {level}• {heading.get('title', 'Untitled')}")
                if len(headings) > 5:
                    print(f"     ... and {len(headings) - 5} more headings")
        
        # Content analysis
        content_analysis = result.get('content_analysis', {})
        if content_analysis and content_analysis.get('status') == 'success':
            analysis_result = content_analysis.get('analysis_result', {})
            print(f"\n🔬 Content Analysis:")
            
            key_requirements = analysis_result.get('key_requirements', [])
            print(f"   • Key Requirements Found: {len(key_requirements)}")
            
            compliance_elements = analysis_result.get('compliance_elements', [])
            print(f"   • Compliance Elements: {len(compliance_elements)}")
            
            if compliance_elements:
                compliance_keywords = list(set([elem.get('keyword', '') for elem in compliance_elements[:5]]))
                print(f"   • Compliance Keywords: {', '.join(compliance_keywords)}")
        
        # Prompt generation
        prompt_generation = result.get('prompt_generation', {})
        if prompt_generation and prompt_generation.get('status') == 'success':
            section_prompts = prompt_generation.get('section_prompts', {})
            print(f"\n💡 Prompt Generation:")
            print(f"   • Total Prompts Generated: {len(section_prompts)}")
            
            if section_prompts:
                print(f"   • Sample Sections with Prompts:")
                for i, section_name in enumerate(list(section_prompts.keys())[:3]):
                    print(f"     • {section_name}")
                if len(section_prompts) > 3:
                    print(f"     ... and {len(section_prompts) - 3} more sections")
        
        # Quality validation
        quality_validation = result.get('quality_validation', {})
        if quality_validation and quality_validation.get('status') == 'success':
            validation_report = quality_validation.get('validation_report', {})
            print(f"\n✅ Quality Assurance:")
            print(f"   • Overall Quality Score: {validation_report.get('overall_quality_score', 0):.2f}")
            print(f"   • Checks Passed: {len(validation_report.get('passed_checks', []))}")
            print(f"   • Checks Failed: {len(validation_report.get('failed_checks', []))}")
            print(f"   • Issues Found: {len(validation_report.get('issues_found', []))}")
            print(f"   • Recommendations: {len(validation_report.get('recommendations', []))}")
        
        # Error handling
        if status == 'error':
            error_msg = result.get('error', 'Unknown error')
            failed_step = result.get('failed_at_step', 'Unknown step')
            print(f"\n❌ Error Details:")
            print(f"   • Failed at: {failed_step}")
            print(f"   • Error: {error_msg}")
        
        print("\n" + "="*60)
        print("🎉 Demo completed successfully!" if status == 'success' else "💥 Demo completed with errors")
        print("="*60)
    
    async def run_system_health_check(self):
        """Run a comprehensive system health check"""
        print("\n🏥 Running System Health Check...")
        print("-" * 40)
        
        try:
            # Check workflow status
            workflow_status = document_processing_workflow.get_workflow_status()
            print(f"✅ Workflow Status: {workflow_status['status']}")
            print(f"✅ Agents Available: {len(workflow_status['agents'])}")
            
            # Check services
            print("✅ Document Processor: Available")
            print("✅ Azure OpenAI Service: Configured")
            print("✅ Qdrant Service: Configured")
            
            print("✅ System health check passed!")
            return True
            
        except Exception as e:
            print(f"❌ System health check failed: {str(e)}")
            return False


async def main():
    """Main demo function"""
    demo = DocumentProcessingDemo()
    
    print("🔧 Running system health check first...")
    health_ok = await demo.run_system_health_check()
    
    if not health_ok:
        print("⚠️ System health check failed. Please check your configuration.")
        print("Run: python tests/run_tests.py --health-check for detailed diagnostics")
        return
    
    print("\n🚀 Starting comprehensive demo...")
    await demo.run_demo()
    
    print("\n📚 Next Steps:")
    print("1. Start the UI: streamlit run ui/document_processing_app.py")
    print("2. Upload your own RFP/RFO documents")
    print("3. Explore the interactive features")
    print("4. Customize prompts and settings")
    print("\n📖 For more information, see DOCUMENT_PROCESSING_README.md")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Demo failed: {str(e)}")
        print("Please check your configuration and try again.")
        print("Run: python tests/run_tests.py --health-check for diagnostics")
